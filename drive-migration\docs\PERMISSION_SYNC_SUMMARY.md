# Tóm Tắt Những Thay Đổi và Cách Đồng Bộ Hóa Permission

## 📋 Tổng Quan

Tài liệu này tóm tắt những thay đổi quan trọng trong hệ thống đồng bộ hóa permission và cách thức hoạt động của module permission synchronization trong dự án migration từ Google Drive sang Lark.

## 🔄 Những Thay Đổi Chính

### 1. Cải Tiến Database Schema

#### Bảng `scanned_lark_files` - Thêm các cột tracking permission mới:

```sql
-- C<PERSON><PERSON> cột mới được thêm vào
successful_permissions INTEGER DEFAULT 0,  -- <PERSON><PERSON> lượng permission đồng bộ thành công
failed_permissions INTEGER DEFAULT 0,      -- Số lượng permission đồng bộ lỗi
skipped_permissions INTEGER DEFAULT 0,     -- Số lượng permission bỏ qua
skipped_messages JSONB,                    -- Thông tin chi tiết permission bỏ qua
```

#### Index tối ưu hóa:

```sql
-- Index cho việc query error cases
CREATE INDEX idx_scanned_lark_files_failed_permissions
ON scanned_lark_files(failed_permissions) WHERE failed_permissions > 0;

CREATE INDEX idx_scanned_lark_files_skipped_permissions
ON scanned_lark_files(skipped_permissions) WHERE skipped_permissions > 0;
```

### 2. Cải Tiến Migration Engine

#### Module `MigrationEngine.syncFilePermissions()`:

- **Improved tracking**: Theo dõi chi tiết success/failure/skip cho từng permission
- **Detailed logging**: Ghi log chi tiết cho mỗi permission operation
- **Error handling**: Xử lý lỗi robust hơn với retry logic
- **Progress reporting**: Real-time progress updates

#### Tính năng mới:

```javascript
// Ví dụ kết quả sync permission
{
  success: true,
  larkFileToken: "doccnXXXXXXX",
  permissionsCount: 5,
  successfulPermissions: 3,
  failedPermissions: 1,
  skippedPermissions: 1,
  skippedMessages: [
    {
      message: "External user not found in Lark workspace",
      googlePermission: {
        role: "viewer",
        type: "user",
        emailAddress: "<EMAIL>"
      }
    }
  ],
  permissionErrors: [
    {
      message: "Permission denied for user",
      googlePermission: {
        role: "editor",
        type: "user",
        emailAddress: "<EMAIL>"
      }
    }
  ]
}
```

### 3. Real-time Progress Tracking

#### Broadcast System:

- **Permission sync progress**: Real-time updates cho UI
- **Error notifications**: Thông báo lỗi ngay lập tức
- **Completion status**: Trạng thái hoàn thành với detailed metrics

## 🔧 Cách Đồng Bộ Hóa Permission

### 1. Quy Trình Tổng Quan

```mermaid
graph TD
    A[Start Permission Sync] --> B[Initialize Sync Process]
    B --> C[Create Realtime Channel]
    C --> D[Validate Matched Files]
    D --> E[Process Files Sequentially]

    E --> F[Get Google Permissions]
    F --> G[Map to Lark Permissions]
    G --> H[Check User Mapping]

    H --> I{User in Lark?}
    I -->|Yes| J[Set Lark Permission]
    I -->|No| K[Skip & Log]

    J --> L{API Success?}
    L -->|Yes| M[Increment Success]
    L -->|No| N[Increment Failed]
    K --> O[Increment Skipped]

    M --> P[Update Database]
    N --> P
    O --> P

    P --> Q[Broadcast Progress]
    Q --> R{More Files?}
    R -->|Yes| E
    R -->|No| S[Finalize Sync]
    S --> T[Generate Report]
```

### 2. Luồng Hoạt Động Chi Tiết

#### 🚀 **Phase 1: Initialization**

```javascript
// Step 1: Khởi tạo sync process
const syncProcess = {
  id: syncId,
  userEmail: userEmail,
  totalFiles: matchedFiles.length,
  processedFiles: 0,
  successfulFiles: 0,
  failedFiles: 0,
  startedAt: new Date().toISOString(),
  errors: [],
};

// Step 2: Tạo realtime channel
migrationEngine.realtime.createMigrationChannel(syncId);

// Step 3: Broadcast sync started
await migrationEngine.realtime.broadcastStatusChange(
  syncId,
  "syncing_permissions",
  {
    previousStatus: "pending",
    reason: "Permission sync started",
    totalFiles: matchedFiles.length,
  }
);
```

#### 🔄 **Phase 2: File Processing Loop**

```javascript
for (const matchedFile of matchedFiles) {
  try {
    // Step 1: Extract file information
    const { larkFileToken, googlePermissions } = matchedFile;

    // Step 2: Validate permissions exist
    if (!googlePermissions || googlePermissions.length === 0) {
      continue; // Skip files without permissions
    }

    // Step 3: Process individual file
    const result = await syncFilePermissions(
      syncId,
      matchedFile,
      progressCallback
    );

    // Step 4: Update counters
    if (result.success) {
      successfulFiles++;
    } else {
      failedFiles++;
    }

    processedFiles++;

    // Step 5: Broadcast progress
    await broadcastProgress(syncId, {
      processedFiles,
      successfulFiles,
      failedFiles,
      totalFiles: matchedFiles.length,
      currentFile: matchedFile.name,
    });
  } catch (error) {
    // Handle unexpected errors
    failedFiles++;
    console.error(`Error processing file ${matchedFile.name}:`, error);
  }
}
```

#### 🎯 **Phase 3: Individual File Permission Sync**

```javascript
async function syncFilePermissions(syncId, matchedFile, progressCallback) {
  const { larkFileToken, googlePermissions } = matchedFile;

  // Initialize counters
  let successfulPermissions = 0;
  let failedPermissions = 0;
  let skippedPermissions = 0;
  const permissionErrors = [];
  const skippedMessages = [];

  // Process each Google permission
  for (const googlePermission of googlePermissions) {
    try {
      // Step 1: Validate permission format
      if (!validateGooglePermission(googlePermission)) {
        skippedPermissions++;
        skippedMessages.push({
          message: "Invalid permission format",
          googlePermission,
        });
        continue;
      }

      // Step 2: Check if user exists in Lark workspace
      const userMapping = await getUserMapping(googlePermission.emailAddress);
      if (!userMapping) {
        skippedPermissions++;
        skippedMessages.push({
          message: "User not found in Lark workspace",
          googlePermission: {
            role: googlePermission.role,
            type: googlePermission.type,
            emailAddress: googlePermission.emailAddress,
          },
        });
        continue;
      }

      // Step 3: Map Google role to Lark role
      const larkRole = mapGoogleRoleToLark(googlePermission.role);
      if (!larkRole) {
        skippedPermissions++;
        skippedMessages.push({
          message: "Cannot map Google role to Lark role",
          googlePermission,
        });
        continue;
      }

      // Step 4: Set permission in Lark
      const permissionResult = await larkService.setFilePermission(
        larkFileToken,
        userMapping.lark_user_id,
        larkRole
      );

      if (permissionResult.success) {
        successfulPermissions++;
        console.log(
          `✅ Permission set: ${googlePermission.emailAddress} → ${larkRole}`
        );
      } else {
        failedPermissions++;
        permissionErrors.push({
          message: permissionResult.error || "Unknown permission error",
          googlePermission: {
            role: googlePermission.role,
            type: googlePermission.type,
            emailAddress: googlePermission.emailAddress,
          },
        });
        console.log(`❌ Permission failed: ${permissionResult.error}`);
      }
    } catch (error) {
      failedPermissions++;
      permissionErrors.push({
        message: error.message || "Exception during permission setting",
        googlePermission,
      });
      console.error(`Exception setting permission:`, error);
    }
  }

  // Return detailed result
  return {
    success: failedPermissions === 0,
    larkFileToken,
    permissionsCount: googlePermissions.length,
    successfulPermissions,
    failedPermissions,
    skippedPermissions,
    permissionErrors,
    skippedMessages,
  };
}
```

#### 💾 **Phase 4: Database Update**

```javascript
async function updateFilesPermissionStatus(userEmail, matchedFiles, results) {
  for (let i = 0; i < matchedFiles.length; i++) {
    const matchedFile = matchedFiles[i];
    const result = results[i];

    if (!result) continue;

    try {
      // Prepare update data
      const updateData = {
        sync_attempted_at: new Date().toISOString(),
        synced_permissions: result.success,
        successful_permissions: result.successfulPermissions || 0,
        failed_permissions: result.failedPermissions || 0,
        skipped_permissions: result.skippedPermissions || 0,
        skipped_messages: result.skippedMessages || null,
        permission_errors: result.success ? null : result.permissionErrors,
        updated_at: new Date().toISOString(),
      };

      // Update database
      const { error } = await supabaseClient
        .getServiceClient()
        .from("scanned_lark_files")
        .update(updateData)
        .eq("user_email", userEmail)
        .eq("token", matchedFile.larkFileToken);

      if (error) {
        console.error(`Database update failed for ${matchedFile.name}:`, error);
      } else {
        console.log(`✅ Database updated for ${matchedFile.name}`);
      }
    } catch (error) {
      console.error(
        `Exception updating database for ${matchedFile.name}:`,
        error
      );
    }
  }
}
```

#### 📡 **Phase 5: Real-time Broadcasting**

```javascript
// Progress update
await migrationEngine.realtime.broadcastMigrationProgress(syncId, {
  type: "permission_sync_progress",
  data: {
    processedFiles,
    totalFiles,
    successfulFiles,
    failedFiles,
    currentFile: matchedFile.name,
    currentFileProgress: {
      successfulPermissions: result.successfulPermissions,
      failedPermissions: result.failedPermissions,
      skippedPermissions: result.skippedPermissions,
    },
  },
});

// Error notification (if needed)
if (result.failedPermissions > 0) {
  await migrationEngine.realtime.broadcastError(syncId, {
    severity: "warning",
    errorMessage: `${result.failedPermissions} permissions failed for ${matchedFile.name}`,
    retryable: true,
    errorDetails: result.permissionErrors,
  });
}
```

#### 🎉 **Phase 6: Finalization**

```javascript
async function finalizeSyncPermissions(syncId, results) {
  // Calculate final statistics
  const totalFiles = results.length;
  const successfulFiles = results.filter((r) => r && r.success).length;
  const failedFiles = results.filter((r) => r && !r.success).length;

  const totalPermissions = results.reduce(
    (sum, r) => sum + (r?.permissionsCount || 0),
    0
  );
  const successfulPermissions = results.reduce(
    (sum, r) => sum + (r?.successfulPermissions || 0),
    0
  );
  const failedPermissions = results.reduce(
    (sum, r) => sum + (r?.failedPermissions || 0),
    0
  );
  const skippedPermissions = results.reduce(
    (sum, r) => sum + (r?.skippedPermissions || 0),
    0
  );

  // Generate final report
  const finalResult = {
    syncId,
    status: failedFiles === 0 ? "completed" : "completed_with_errors",
    summary: {
      totalFiles,
      successfulFiles,
      failedFiles,
      totalPermissions,
      successfulPermissions,
      failedPermissions,
      skippedPermissions,
      successRate:
        totalPermissions > 0
          ? ((successfulPermissions / totalPermissions) * 100).toFixed(2)
          : 0,
    },
    completedAt: new Date().toISOString(),
  };

  // Broadcast completion
  await migrationEngine.realtime.broadcastMigrationComplete(
    syncId,
    finalResult
  );

  return finalResult;
}
```

### 3. State Machine và Error Handling

#### 🔄 **Permission Sync State Flow**

```mermaid
stateDiagram-v2
    [*] --> Initializing
    Initializing --> Processing : Files validated
    Processing --> MappingPermissions : For each file
    MappingPermissions --> CheckingUser : Permission extracted
    CheckingUser --> SettingPermission : User found
    CheckingUser --> Skipping : User not found
    SettingPermission --> Success : API success
    SettingPermission --> Failed : API error
    SettingPermission --> Retrying : Temporary error
    Retrying --> SettingPermission : Retry attempt
    Retrying --> Failed : Max retries exceeded
    Skipping --> NextPermission : Log skip reason
    Success --> NextPermission : Increment success
    Failed --> NextPermission : Increment failed
    NextPermission --> MappingPermissions : More permissions
    NextPermission --> UpdateDatabase : File complete
    UpdateDatabase --> Broadcasting : DB updated
    Broadcasting --> Processing : More files
    Broadcasting --> Completed : All files done
    Completed --> [*]
```

#### ⚠️ **Error Recovery Strategy**

```javascript
// Retry configuration
const RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
  backoffFactor: 2, // Exponential backoff
};

async function setPermissionWithRetry(
  larkFileToken,
  userId,
  role,
  retryCount = 0
) {
  try {
    const result = await larkService.setFilePermission(
      larkFileToken,
      userId,
      role
    );

    if (result.success) {
      return { success: true, data: result.data };
    }

    // Check if error is retryable
    if (
      isRetryableError(result.error) &&
      retryCount < RETRY_CONFIG.maxRetries
    ) {
      const delay = Math.min(
        RETRY_CONFIG.baseDelay *
          Math.pow(RETRY_CONFIG.backoffFactor, retryCount),
        RETRY_CONFIG.maxDelay
      );

      console.log(
        `Retrying permission set in ${delay}ms (attempt ${retryCount + 1})`
      );
      await sleep(delay);

      return setPermissionWithRetry(
        larkFileToken,
        userId,
        role,
        retryCount + 1
      );
    }

    return { success: false, error: result.error, retryable: false };
  } catch (error) {
    if (retryCount < RETRY_CONFIG.maxRetries && isNetworkError(error)) {
      const delay = Math.min(
        RETRY_CONFIG.baseDelay *
          Math.pow(RETRY_CONFIG.backoffFactor, retryCount),
        RETRY_CONFIG.maxDelay
      );

      console.log(`Network error, retrying in ${delay}ms:`, error.message);
      await sleep(delay);

      return setPermissionWithRetry(
        larkFileToken,
        userId,
        role,
        retryCount + 1
      );
    }

    return { success: false, error: error.message, retryable: false };
  }
}

function isRetryableError(error) {
  const retryableMessages = [
    "rate limit exceeded",
    "temporary unavailable",
    "service temporarily overloaded",
    "request timeout",
  ];

  return retryableMessages.some((msg) => error.toLowerCase().includes(msg));
}

function isNetworkError(error) {
  return (
    error.code === "ECONNRESET" ||
    error.code === "ETIMEDOUT" ||
    error.code === "ENOTFOUND"
  );
}
```

### 4. Permission Mapping Logic

#### 🗂️ **Google to Lark Role Mapping**

```javascript
const PERMISSION_MAPPING = {
  // Google Drive roles → Lark roles
  owner: "full_access",
  writer: "edit",
  editor: "edit",
  commenter: "comment",
  reader: "view",
  viewer: "view",
};

function mapGoogleRoleToLark(googleRole) {
  const normalizedRole = googleRole.toLowerCase();
  const larkRole = PERMISSION_MAPPING[normalizedRole];

  if (!larkRole) {
    console.warn(`Unknown Google role: ${googleRole}, defaulting to 'view'`);
    return "view";
  }

  return larkRole;
}

// Special cases handling
function shouldSkipPermission(googlePermission, userMapping) {
  // Skip if no user mapping
  if (!userMapping) {
    return {
      skip: true,
      reason: "User not found in Lark workspace",
      code: "USER_NOT_FOUND",
    };
  }

  // Skip if user is deactivated
  if (userMapping.status === "deactivated") {
    return {
      skip: true,
      reason: "User is deactivated in Lark",
      code: "USER_DEACTIVATED",
    };
  }

  // Skip domain-based permissions for external users
  if (
    googlePermission.type === "domain" &&
    !isInternalDomain(googlePermission.domain)
  ) {
    return {
      skip: true,
      reason: "External domain permission not supported",
      code: "EXTERNAL_DOMAIN",
    };
  }

  // Skip anonymous permissions
  if (googlePermission.type === "anyone") {
    return {
      skip: true,
      reason: "Anonymous access not supported in Lark",
      code: "ANONYMOUS_ACCESS",
    };
  }

  return { skip: false };
}
```

### 5. Concurrency Control

#### 🚦 **Semaphore Implementation**

```javascript
class PermissionSyncSemaphore {
  constructor(maxConcurrency = 5) {
    this.maxConcurrency = maxConcurrency;
    this.currentConcurrency = 0;
    this.queue = [];
  }

  async acquire() {
    return new Promise((resolve) => {
      if (this.currentConcurrency < this.maxConcurrency) {
        this.currentConcurrency++;
        resolve(() => this.release());
      } else {
        this.queue.push(resolve);
      }
    });
  }

  release() {
    this.currentConcurrency--;
    if (this.queue.length > 0) {
      const next = this.queue.shift();
      this.currentConcurrency++;
      next(() => this.release());
    }
  }
}

// Usage in permission sync
const semaphore = new PermissionSyncSemaphore(3); // Max 3 concurrent permission operations

async function syncFilePermissionsWithControl(
  syncId,
  matchedFile,
  progressCallback
) {
  const release = await semaphore.acquire();

  try {
    return await syncFilePermissions(syncId, matchedFile, progressCallback);
  } finally {
    release();
  }
}
```

### 6. Real-time Progress Tracking

#### 📡 **WebSocket Events**

```javascript
// Event types for permission sync
const SYNC_EVENTS = {
  SYNC_STARTED: "permission_sync_started",
  SYNC_PROGRESS: "permission_sync_progress",
  SYNC_FILE_COMPLETE: "permission_sync_file_complete",
  SYNC_ERROR: "permission_sync_error",
  SYNC_COMPLETED: "permission_sync_completed",
};

// Progress event structure
const progressEvent = {
  type: SYNC_EVENTS.SYNC_PROGRESS,
  syncId: "sync_123",
  timestamp: new Date().toISOString(),
  data: {
    totalFiles: 100,
    processedFiles: 45,
    successfulFiles: 42,
    failedFiles: 3,
    currentFile: {
      name: "document.pdf",
      token: "doccnXXXXXX",
      totalPermissions: 5,
      processedPermissions: 3,
      successfulPermissions: 2,
      failedPermissions: 0,
      skippedPermissions: 1,
    },
    overallProgress: {
      percentage: 45,
      estimatedTimeRemaining: "2m 30s",
      avgTimePerFile: "1.2s",
    },
  },
};

// Client-side event listener
migrationEngine.realtime.subscribeToMigration(syncId, (event) => {
  switch (event.type) {
    case SYNC_EVENTS.SYNC_PROGRESS:
      updateProgressUI(event.data);
      break;
    case SYNC_EVENTS.SYNC_ERROR:
      showErrorNotification(event.data);
      break;
    case SYNC_EVENTS.SYNC_COMPLETED:
      showCompletionSummary(event.data);
      break;
  }
});
```

### 7. Performance Optimization

#### ⚡ **Batch Processing Strategy**

```javascript
class OptimizedPermissionSync {
  constructor() {
    this.batchSize = 10; // Process 10 files at a time
    this.permissionDelay = 100; // 100ms delay between permission calls
    this.rateLimitWindow = 60000; // 1 minute window
    this.maxRequestsPerWindow = 100; // Max 100 requests per minute
  }

  async processBatchWithRateLimit(files) {
    const batches = this.createBatches(files, this.batchSize);
    const results = [];

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      console.log(`Processing batch ${i + 1}/${batches.length}`);

      // Process batch sequentially to avoid overwhelming API
      const batchResults = [];
      for (const file of batch) {
        const result = await this.syncFilePermissionsWithControl(file);
        batchResults.push(result);

        // Rate limiting delay
        if (batchResults.length < batch.length) {
          await this.sleep(this.permissionDelay);
        }
      }

      results.push(...batchResults);

      // Longer delay between batches
      if (i < batches.length - 1) {
        await this.sleep(1000); // 1 second between batches
      }
    }

    return results;
  }

  createBatches(array, batchSize) {
    const batches = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }
}
```

### 8. Monitoring và Alerting

#### 📊 **Performance Metrics Collection**

```javascript
class PermissionSyncMetrics {
  constructor() {
    this.metrics = {
      totalSyncs: 0,
      successfulSyncs: 0,
      failedSyncs: 0,
      avgSyncTime: 0,
      avgPermissionsPerFile: 0,
      errorRates: new Map(),
      performanceHistory: [],
    };
  }

  recordSyncResult(syncResult) {
    const { syncId, duration, totalFiles, successfulFiles, failedFiles } =
      syncResult;

    this.metrics.totalSyncs++;
    this.metrics.successfulSyncs += successfulFiles;
    this.metrics.failedSyncs += failedFiles;

    // Update averages
    this.updateAverages(duration, totalFiles);

    // Record errors
    this.recordErrors(syncResult.errors);

    // Performance history (keep last 100 syncs)
    this.metrics.performanceHistory.push({
      syncId,
      timestamp: new Date().toISOString(),
      duration,
      totalFiles,
      successfulFiles,
      failedFiles,
      successRate: ((successfulFiles / totalFiles) * 100).toFixed(2),
    });

    if (this.metrics.performanceHistory.length > 100) {
      this.metrics.performanceHistory.shift();
    }
  }

  getHealthStatus() {
    const recentSyncs = this.metrics.performanceHistory.slice(-10);
    const avgSuccessRate =
      recentSyncs.reduce((sum, sync) => sum + parseFloat(sync.successRate), 0) /
      recentSyncs.length;

    return {
      status:
        avgSuccessRate > 95
          ? "healthy"
          : avgSuccessRate > 80
          ? "warning"
          : "critical",
      avgSuccessRate: avgSuccessRate.toFixed(2),
      totalSyncs: this.metrics.totalSyncs,
      avgSyncTime: this.metrics.avgSyncTime,
      topErrors: this.getTopErrors(),
    };
  }
}
```

### 9. Troubleshooting Guide

#### 🔍 **Common Issues và Solutions**

| Issue                   | Symptoms                 | Root Cause                       | Solution                                    |
| ----------------------- | ------------------------ | -------------------------------- | ------------------------------------------- |
| **High Skip Rate**      | >50% permissions skipped | External users, invalid mappings | Update user mapping, validate email domains |
| **API Rate Limits**     | 429 errors, timeouts     | Too many concurrent requests     | Implement stricter rate limiting            |
| **Permission Failures** | 403 errors               | Insufficient Lark permissions    | Check admin permissions, validate tokens    |
| **Slow Performance**    | Long sync times          | Large files, many permissions    | Optimize batch size, implement caching      |
| **Memory Issues**       | Process crashes          | Large datasets                   | Implement streaming, reduce batch sizes     |

#### 🛠️ **Diagnostic Commands**

```javascript
// Check sync health
const health = await migrationEngine.getPermissionSyncHealth();
console.log("Sync Health:", health);

// Get error analysis
const errorAnalysis = await migrationEngine.analyzePermissionErrors(userEmail);
console.log("Error Analysis:", errorAnalysis);

// Performance report
const performance = await migrationEngine.getPermissionSyncPerformance();
console.log("Performance Report:", performance);
```

```javascript
await migrationEngine.startSyncPermissions(
  syncId, // Unique sync identifier
  userEmail, // User email thực hiện sync
  matchedFiles, // Array files đã match Google-Lark
  progressCallback // Callback cho progress updates
);
```

#### b) Xử lý từng file:

```javascript
const result = await migrationEngine.syncFilePermissions(
  syncId,
  matchedFile, // {larkFileToken, googlePermissions}
  progressCallback
);
```

#### c) Mapping Permission Google → Lark:

- **Google Roles**: owner, writer, reader, commenter
- **Lark Roles**: full_access, edit, view, comment
- **External Users**: Xử lý đặc biệt cho user ngoài workspace

### 3. Database Operations

#### a) Update Permission Status:

```javascript
// Update successful sync
await supabaseClient
  .from("scanned_lark_files")
  .update({
    synced_permissions: true,
    successful_permissions: result.successfulPermissions,
    failed_permissions: result.failedPermissions,
    skipped_permissions: result.skippedPermissions,
    skipped_messages: result.skippedMessages,
    updated_at: new Date().toISOString(),
  })
  .eq("token", larkFileToken);
```

#### b) Query Permission Statistics:

```sql
-- Thống kê permission sync
SELECT
  COUNT(*) as total_files,
  SUM(successful_permissions) as total_successful,
  SUM(failed_permissions) as total_failed,
  SUM(skipped_permissions) as total_skipped,
  COUNT(*) FILTER (WHERE failed_permissions > 0) as files_with_errors
FROM scanned_lark_files;
```

## 📊 Monitoring và Troubleshooting

### 1. Permission Sync Metrics

#### Queries hữu ích:

```sql
-- Files có permission errors
SELECT token, name, failed_permissions, skipped_permissions, skipped_messages
FROM scanned_lark_files
WHERE failed_permissions > 0 OR skipped_permissions > 0;

-- Top error patterns
SELECT
  permission_errors->>'message' as error_type,
  COUNT(*) as occurrences
FROM scanned_lark_files
WHERE permission_errors IS NOT NULL
GROUP BY permission_errors->>'message'
ORDER BY occurrences DESC;

-- Permission sync success rate by user
SELECT
  user_email,
  COUNT(*) as total_files,
  SUM(successful_permissions) as total_successful,
  SUM(failed_permissions) as total_failed,
  SUM(skipped_permissions) as total_skipped,
  ROUND(
    SUM(successful_permissions)::DECIMAL /
    NULLIF(SUM(successful_permissions + failed_permissions + skipped_permissions), 0) * 100,
    2
  ) as success_rate_percent
FROM scanned_lark_files
WHERE successful_permissions > 0 OR failed_permissions > 0 OR skipped_permissions > 0
GROUP BY user_email
ORDER BY success_rate_percent DESC;
```

### 2. Real-time Monitoring Dashboard

#### 📈 **Key Performance Indicators (KPIs)**

```javascript
// Dashboard metrics calculation
async function calculateSyncKPIs(timeframe = "24h") {
  const kpis = {
    // Volume metrics
    totalSyncsInitiated: 0,
    totalFilesProcessed: 0,
    totalPermissionsProcessed: 0,

    // Success metrics
    successfulSyncs: 0,
    successfulPermissions: 0,
    successRate: 0,

    // Error metrics
    failedSyncs: 0,
    failedPermissions: 0,
    skippedPermissions: 0,
    errorRate: 0,

    // Performance metrics
    avgSyncDuration: 0,
    avgPermissionsPerFile: 0,
    throughputPerHour: 0,

    // Trending
    trend: {
      syncVolume: "increasing",
      successRate: "stable",
      errorRate: "decreasing",
    },
  };

  // Query database for metrics
  const { data: syncStats } = await supabaseClient
    .from("scanned_lark_files")
    .select(
      `
      successful_permissions,
      failed_permissions,
      skipped_permissions,
      created_at
    `
    )
    .gte("created_at", getTimeframeStart(timeframe));

  // Calculate metrics
  kpis.totalFilesProcessed = syncStats.length;
  kpis.totalPermissionsProcessed = syncStats.reduce(
    (sum, row) =>
      sum +
      (row.successful_permissions +
        row.failed_permissions +
        row.skipped_permissions),
    0
  );
  kpis.successfulPermissions = syncStats.reduce(
    (sum, row) => sum + row.successful_permissions,
    0
  );
  kpis.failedPermissions = syncStats.reduce(
    (sum, row) => sum + row.failed_permissions,
    0
  );
  kpis.skippedPermissions = syncStats.reduce(
    (sum, row) => sum + row.skipped_permissions,
    0
  );

  kpis.successRate =
    kpis.totalPermissionsProcessed > 0
      ? (
          (kpis.successfulPermissions / kpis.totalPermissionsProcessed) *
          100
        ).toFixed(2)
      : 0;
  kpis.errorRate =
    kpis.totalPermissionsProcessed > 0
      ? (
          (kpis.failedPermissions / kpis.totalPermissionsProcessed) *
          100
        ).toFixed(2)
      : 0;

  return kpis;
}
```

### 3. Advanced Error Analysis

#### 🔍 **Error Pattern Detection**

```javascript
class PermissionErrorAnalyzer {
  async analyzeErrors(timeframe = "7d") {
    const errors = await this.getErrorData(timeframe);

    return {
      errorCategories: this.categorizeErrors(errors),
      errorTrends: this.analyzeErrorTrends(errors),
      userImpactAnalysis: this.analyzeUserImpact(errors),
      recommendations: this.generateRecommendations(errors),
    };
  }

  categorizeErrors(errors) {
    const categories = {
      USER_NOT_FOUND: { count: 0, percentage: 0, examples: [] },
      PERMISSION_DENIED: { count: 0, percentage: 0, examples: [] },
      RATE_LIMIT: { count: 0, percentage: 0, examples: [] },
      NETWORK_ERROR: { count: 0, percentage: 0, examples: [] },
      INVALID_TOKEN: { count: 0, percentage: 0, examples: [] },
      OTHER: { count: 0, percentage: 0, examples: [] },
    };

    errors.forEach((error) => {
      const category = this.categorizeError(error.message);
      categories[category].count++;
      categories[category].examples.push(error);
    });

    const totalErrors = errors.length;
    Object.keys(categories).forEach((category) => {
      categories[category].percentage = (
        (categories[category].count / totalErrors) *
        100
      ).toFixed(2);
    });

    return categories;
  }

  generateRecommendations(errors) {
    const recommendations = [];

    // High user not found rate
    const userNotFoundRate = this.getErrorRate(errors, "USER_NOT_FOUND");
    if (userNotFoundRate > 20) {
      recommendations.push({
        priority: "high",
        issue: "High user not found rate",
        suggestion: "Update user mapping table or validate email addresses",
        impact: "Reduced permission sync success rate",
      });
    }

    // Rate limiting issues
    const rateLimitRate = this.getErrorRate(errors, "RATE_LIMIT");
    if (rateLimitRate > 5) {
      recommendations.push({
        priority: "medium",
        issue: "API rate limiting detected",
        suggestion:
          "Implement more aggressive rate limiting or request quota increase",
        impact: "Slower sync performance",
      });
    }

    return recommendations;
  }
}
```

### 4. Performance Optimization Strategies

#### ⚡ **Dynamic Rate Limiting**

```javascript
class AdaptiveRateLimiter {
  constructor() {
    this.baseDelay = 100; // Base delay between requests
    this.currentDelay = this.baseDelay;
    this.errorWindow = []; // Track recent errors
    this.successWindow = []; // Track recent successes
    this.windowSize = 50; // Size of sliding window
  }

  async executeWithAdaptiveLimit(apiCall) {
    // Wait for current delay
    await this.sleep(this.currentDelay);

    try {
      const result = await apiCall();

      // Record success
      this.recordSuccess();

      // Decrease delay if performing well
      if (this.getRecentErrorRate() < 0.05) {
        // Less than 5% error rate
        this.currentDelay = Math.max(this.baseDelay, this.currentDelay * 0.9);
      }

      return result;
    } catch (error) {
      // Record error
      this.recordError(error);

      // Increase delay based on error type
      if (this.isRateLimitError(error)) {
        this.currentDelay = Math.min(5000, this.currentDelay * 2); // Max 5 second delay
      } else if (this.isNetworkError(error)) {
        this.currentDelay = Math.min(2000, this.currentDelay * 1.5);
      }

      throw error;
    }
  }

  getRecentErrorRate() {
    const recentResults = [...this.errorWindow, ...this.successWindow]
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, this.windowSize);

    if (recentResults.length === 0) return 0;

    const errorCount = recentResults.filter((r) => r.type === "error").length;
    return errorCount / recentResults.length;
  }
}
```

### 2. Common Issues và Solutions

#### a) External Users:

- **Issue**: Users ngoài Lark workspace không thể add permission
- **Solution**: Automatic skip với detailed logging trong `skipped_messages`

#### b) Permission Denied:

- **Issue**: Lark API trả về permission denied
- **Solution**: Retry với different permission levels, log failures

#### c) Network Timeouts:

- **Issue**: API calls timeout khi sync large batches
- **Solution**: Implement semaphore với controlled concurrency

### 3. Performance Optimization

#### a) Batch Processing:

- Process files sequentially to avoid API rate limits
- Use controlled concurrency với Semaphore pattern
- Implement exponential backoff cho retries

#### b) Database Optimization:

- Partial indexes cho error cases
- JSONB indexing cho permission queries
- Efficient bulk updates

## 🔐 Security Considerations

### 1. Permission Validation:

- Validate Google permissions trước khi mapping
- Ensure proper Lark workspace membership
- Log all permission changes for audit

### 2. Error Handling:

- Sanitize error messages to avoid sensitive data leakage
- Implement proper retry logic with backoff
- Graceful degradation khi services unavailable

### 3. Data Privacy:

- Store minimal user information
- Encrypt sensitive permission data
- Implement proper data retention policies

## 🚀 Usage Examples

### 1. Start Permission Sync:

```javascript
const syncId = generateUUID();
const matchedFiles = await findMatchedFiles(userEmail);

const result = await migrationEngine.startSyncPermissions(
  syncId,
  userEmail,
  matchedFiles,
  (progress) => {
    console.log(`Progress: ${progress.processedFiles}/${progress.totalFiles}`);
  }
);

console.log("Sync completed:", result);
```

### 2. Monitor Progress:

```javascript
// Listen for real-time updates
migrationEngine.realtime.subscribeToMigration(syncId, (update) => {
  if (update.type === "permission_sync_progress") {
    updateUI(update.data);
  }
});
```

### 3. Query Results:

```javascript
// Get sync statistics
const stats = await supabaseClient.rpc("get_permission_sync_stats", {
  user_email: userEmail,
});

console.log("Permission sync statistics:", stats);
```

## 📚 Related Documentation

- [`permission-tracking-enhancement.md`](./permission-tracking-enhancement.md) - Chi tiết về database schema changes
- [`database-schema.md`](./database-schema.md) - Tổng quan database schema
- [`implementation-summary.md`](./implementation-summary.md) - Tổng quan implementation
- [`LARK_UPLOAD_GUIDE.md`](./LARK_UPLOAD_GUIDE.md) - Hướng dẫn upload files lên Lark

## 🔄 Migration Process Flow

### Complete Permission Sync Workflow:

1. **Initialization**: Create sync session và realtime channel
2. **File Processing**: Process matched files in controlled batches
3. **Permission Mapping**: Map Google permissions to Lark equivalents
4. **API Calls**: Execute Lark permission API calls
5. **Result Tracking**: Track success/failure/skip counts
6. **Database Updates**: Update permission sync status
7. **Progress Broadcasting**: Real-time progress updates
8. **Finalization**: Complete sync với detailed summary

### Error Recovery:

- Automatic retry cho transient failures
- Skip external users với detailed logging
- Graceful handling của API rate limits
- Comprehensive error reporting

---

**Last Updated**: July 17, 2025  
**Version**: 1.0  
**Author**: Migration Engine Team
