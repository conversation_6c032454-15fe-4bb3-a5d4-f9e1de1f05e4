#!/usr/bin/env node

/**
 * Script để tạo các bảng download cho domain osp.com.vn
 */

import dotenv from 'dotenv';
import { SupabaseClient } from './src/database/supabase.js';

dotenv.config();

async function createOspComVnDownloadTables() {
    console.log('🚀 Creating osp.com.vn download tables...');
    console.log('='.repeat(60));
    
    try {
        const supabase = new SupabaseClient();
        const serviceClient = supabase.getServiceClient();
        
        // Test connection
        console.log('🔌 Testing database connection...');
        const { data: testData, error: testError } = await serviceClient
            .from(process.env.SCANNED_FILES_TABLE_NAME)
            .select('id')
            .limit(1);
            
        if (testError) {
            throw new Error(`Database connection failed: ${testError.message}`);
        }
        console.log('✅ Database connection successful');
        
        // Create download_sessions table
        console.log('');
        console.log('📦 Creating download_sessions table...');
        
        const downloadSessionsSQL = `
        CREATE TABLE IF NOT EXISTS ${process.env.DOWNLOAD_SESSIONS_TABLE_NAME} (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name TEXT NOT NULL,
          selected_users TEXT[] NOT NULL,
          download_path TEXT NOT NULL,
          concurrent_downloads INTEGER DEFAULT 3,
          max_retries INTEGER DEFAULT 3,
          skip_mime_types TEXT[] DEFAULT '{}',
          processing_order TEXT DEFAULT 'created_at' CHECK (processing_order IN ('created_at', 'user_email', 'size_asc', 'size_desc')),
          stop_on_error BOOLEAN DEFAULT false,
          continue_on_error BOOLEAN DEFAULT true,
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'paused', 'completed', 'failed', 'cancelled')),
          total_files INTEGER DEFAULT 0,
          downloaded_files INTEGER DEFAULT 0,
          failed_files INTEGER DEFAULT 0,
          skipped_files INTEGER DEFAULT 0,
          total_size BIGINT DEFAULT 0,
          downloaded_size BIGINT DEFAULT 0,
          current_user_email TEXT,
          current_file_name TEXT,
          error_log JSONB DEFAULT '[]',
          started_at TIMESTAMPTZ,
          completed_at TIMESTAMPTZ,
          created_at TIMESTAMPTZ DEFAULT NOW(),
          updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        `;
        
        const { error: sessionsError } = await serviceClient.rpc('exec_raw_sql', { 
            query: downloadSessionsSQL 
        });
        
        if (sessionsError) {
            console.error('❌ Failed to create download_sessions table:', sessionsError.message);
        } else {
            console.log('✅ Created download_sessions table successfully');
        }
        
        // Create download_items table
        console.log('');
        console.log('📝 Creating download_items table...');
        
        const downloadItemsSQL = `
        CREATE TABLE IF NOT EXISTS ${process.env.DOWNLOAD_ITEMS_TABLE_NAME} (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          download_session_id UUID NOT NULL REFERENCES ${process.env.DOWNLOAD_SESSIONS_TABLE_NAME}(id) ON DELETE CASCADE,
          scanned_file_id BIGINT NOT NULL REFERENCES ${process.env.SCANNED_FILES_TABLE_NAME}(id),
          user_email TEXT NOT NULL,
          file_id TEXT NOT NULL,
          file_name TEXT NOT NULL,
          file_path TEXT NOT NULL,
          local_path TEXT,
          file_size BIGINT DEFAULT 0,
          mime_type TEXT,
          is_folder BOOLEAN DEFAULT FALSE,
          status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'downloading', 'completed', 'failed', 'skipped')),
          retry_count INTEGER DEFAULT 0,
          error_message TEXT,
          web_view_link TEXT,
          export_links TEXT,
          download_started_at TIMESTAMPTZ,
          download_completed_at TIMESTAMPTZ,
          download_duration INTEGER,
          created_at TIMESTAMPTZ DEFAULT NOW()
        );
        `;
        
        const { error: itemsError } = await serviceClient.rpc('exec_raw_sql', { 
            query: downloadItemsSQL 
        });
        
        if (itemsError) {
            console.error('❌ Failed to create download_items table:', itemsError.message);
        } else {
            console.log('✅ Created download_items table successfully');
        }
        
        // Create indexes
        console.log('');
        console.log('🔍 Creating indexes...');
        
        const indexesSQL = `
        CREATE INDEX IF NOT EXISTS idx_${process.env.DOWNLOAD_SESSIONS_TABLE_NAME}_status ON ${process.env.DOWNLOAD_SESSIONS_TABLE_NAME}(status);
        CREATE INDEX IF NOT EXISTS idx_${process.env.DOWNLOAD_SESSIONS_TABLE_NAME}_created_at ON ${process.env.DOWNLOAD_SESSIONS_TABLE_NAME}(created_at DESC);
        CREATE INDEX IF NOT EXISTS idx_${process.env.DOWNLOAD_ITEMS_TABLE_NAME}_session_id ON ${process.env.DOWNLOAD_ITEMS_TABLE_NAME}(download_session_id);
        CREATE INDEX IF NOT EXISTS idx_${process.env.DOWNLOAD_ITEMS_TABLE_NAME}_status ON ${process.env.DOWNLOAD_ITEMS_TABLE_NAME}(status);
        CREATE INDEX IF NOT EXISTS idx_${process.env.DOWNLOAD_ITEMS_TABLE_NAME}_user_email ON ${process.env.DOWNLOAD_ITEMS_TABLE_NAME}(user_email);
        `;
        
        const { error: indexesError } = await serviceClient.rpc('exec_raw_sql', { 
            query: indexesSQL 
        });
        
        if (indexesError) {
            console.error('❌ Failed to create indexes:', indexesError.message);
        } else {
            console.log('✅ Created indexes successfully');
        }
        
        // Update scanned_files table to add download columns if not exist
        console.log('');
        console.log('📄 Updating scanned_files table...');
        
        const updateScannedFilesSQL = `
        ALTER TABLE ${process.env.SCANNED_FILES_TABLE_NAME} 
        ADD COLUMN IF NOT EXISTS download_status TEXT CHECK (download_status IN ('not_downloaded', 'downloaded', 'failed', 'exposed')),
        ADD COLUMN IF NOT EXISTS local_path TEXT,
        ADD COLUMN IF NOT EXISTS downloaded_at TIMESTAMPTZ;
        
        CREATE INDEX IF NOT EXISTS idx_${process.env.SCANNED_FILES_TABLE_NAME}_download_status ON ${process.env.SCANNED_FILES_TABLE_NAME}(download_status);
        `;
        
        const { error: updateError } = await serviceClient.rpc('exec_raw_sql', { 
            query: updateScannedFilesSQL 
        });
        
        if (updateError) {
            console.error('❌ Failed to update scanned_files table:', updateError.message);
        } else {
            console.log('✅ Updated scanned_files table successfully');
        }
        
        // Verify tables were created
        console.log('');
        console.log('🔍 Verifying tables...');
        
        const tables = [
            process.env.DOWNLOAD_SESSIONS_TABLE_NAME,
            process.env.DOWNLOAD_ITEMS_TABLE_NAME
        ];
        
        for (const table of tables) {
            const { data, error } = await serviceClient
                .from(table)
                .select('*')
                .limit(1);
                
            if (error) {
                console.log(`❌ Table ${table}: ${error.message}`);
            } else {
                console.log(`✅ Table ${table}: OK`);
            }
        }
        
        console.log('');
        console.log('🎉 Successfully created osp.com.vn download tables!');
        console.log('');
        console.log('📋 Created tables:');
        console.log(`   - ${process.env.DOWNLOAD_SESSIONS_TABLE_NAME}`);
        console.log(`   - ${process.env.DOWNLOAD_ITEMS_TABLE_NAME}`);
        console.log('📋 Updated tables:');
        console.log(`   - ${process.env.SCANNED_FILES_TABLE_NAME} (added download columns)`);
        
    } catch (error) {
        console.error('💥 Failed to create tables:', error.message);
        console.error(error.stack);
        process.exit(1);
    }
}

// Run the script
createOspComVnDownloadTables();
