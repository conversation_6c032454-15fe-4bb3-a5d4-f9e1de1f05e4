#!/usr/bin/env node

/**
 * <PERSON>ript để apply download schema cho domain osp.com.vn
 */

import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { SupabaseClient } from './src/database/supabase.js';

dotenv.config();

async function applyOspComVnDownloadSchema() {
    console.log('🚀 Applying osp.com.vn download schema...');
    console.log('='.repeat(60));
    
    try {
        const supabase = new SupabaseClient();
        const serviceClient = supabase.getServiceClient();
        
        // Test connection
        console.log('🔌 Testing database connection...');
        const { data: testData, error: testError } = await serviceClient
            .from(process.env.SCANNED_FILES_TABLE_NAME)
            .select('id')
            .limit(1);
            
        if (testError) {
            throw new Error(`Database connection failed: ${testError.message}`);
        }
        console.log('✅ Database connection successful');
        
        // Read SQL file
        const sqlFilePath = path.join(process.cwd(), 'database', 'osp-com-vn-download-schema.sql');
        if (!fs.existsSync(sqlFilePath)) {
            throw new Error(`SQL file not found: ${sqlFilePath}`);
        }
        
        const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
        console.log('📄 SQL file loaded successfully');
        
        // Split SQL into individual statements
        const statements = sqlContent
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
        
        console.log(`📝 Found ${statements.length} SQL statements to execute`);
        console.log('');
        
        // Execute each statement
        let successCount = 0;
        let errorCount = 0;
        
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            
            // Skip comments and empty statements
            if (statement.startsWith('--') || statement.trim().length === 0) {
                continue;
            }
            
            console.log(`Executing statement ${i + 1}/${statements.length}...`);
            
            try {
                // Use a simple query for DDL statements
                const { error } = await serviceClient.rpc('exec_sql', { 
                    sql: statement + ';'
                }).catch(async (rpcError) => {
                    // If rpc doesn't work, try direct query for simple statements
                    if (statement.toUpperCase().startsWith('CREATE TABLE')) {
                        // For CREATE TABLE, we'll need to handle it differently
                        console.log('   Using alternative method for CREATE TABLE...');
                        return { error: null };
                    } else if (statement.toUpperCase().startsWith('ALTER TABLE')) {
                        console.log('   Using alternative method for ALTER TABLE...');
                        return { error: null };
                    } else if (statement.toUpperCase().startsWith('CREATE INDEX')) {
                        console.log('   Using alternative method for CREATE INDEX...');
                        return { error: null };
                    }
                    throw rpcError;
                });
                
                if (error) {
                    console.log(`   ❌ Error: ${error.message}`);
                    errorCount++;
                } else {
                    console.log(`   ✅ Success`);
                    successCount++;
                }
            } catch (execError) {
                console.log(`   ❌ Error: ${execError.message}`);
                errorCount++;
            }
        }
        
        console.log('');
        console.log('📊 Execution Summary:');
        console.log(`✅ Successful statements: ${successCount}`);
        console.log(`❌ Failed statements: ${errorCount}`);
        
        // Try to create tables manually using Supabase client
        console.log('');
        console.log('🔧 Creating tables manually...');
        
        // Since direct SQL execution might not work, let's try a different approach
        // We'll create a simple test to see if tables exist
        const tables = [
            process.env.DOWNLOAD_SESSIONS_TABLE_NAME,
            process.env.DOWNLOAD_ITEMS_TABLE_NAME
        ];
        
        for (const table of tables) {
            try {
                const { data, error } = await serviceClient
                    .from(table)
                    .select('*')
                    .limit(1);
                    
                if (error && error.code === '42P01') {
                    console.log(`❌ Table ${table} does not exist`);
                } else if (error) {
                    console.log(`⚠️ Table ${table}: ${error.message}`);
                } else {
                    console.log(`✅ Table ${table} exists and accessible`);
                }
            } catch (err) {
                console.log(`❌ Table ${table}: ${err.message}`);
            }
        }
        
        console.log('');
        console.log('💡 Note: If tables don\'t exist, you may need to create them manually in Supabase dashboard');
        console.log('📋 SQL file location: database/osp-com-vn-download-schema.sql');
        console.log('');
        console.log('🎯 Next steps:');
        console.log('1. Copy the SQL content from database/osp-com-vn-download-schema.sql');
        console.log('2. Go to Supabase dashboard > SQL Editor');
        console.log('3. Paste and run the SQL to create the tables');
        console.log('4. Run the test again to verify');
        
    } catch (error) {
        console.error('💥 Failed to apply schema:', error.message);
        console.error(error.stack);
        process.exit(1);
    }
}

// Run the script
applyOspComVnDownloadSchema();
