-- OSP.COM.VN Download Tables Schema
-- T<PERSON><PERSON> c<PERSON> bảng download cho domain osp.com.vn

-- Bảng osp_com_vn_download_sessions: Quản lý các session download
CREATE TABLE IF NOT EXISTS osp_com_vn_download_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  selected_users TEXT[] NOT NULL, -- Array of user emails
  download_path TEXT NOT NULL, -- Local path để download
  concurrent_downloads INTEGER DEFAULT 3,
  max_retries INTEGER DEFAULT 3,
  skip_mime_types TEXT[] DEFAULT '{}', -- Array of MIME types to skip
  processing_order TEXT DEFAULT 'created_at' CHECK (processing_order IN ('created_at', 'user_email', 'size_asc', 'size_desc')),
  stop_on_error BOOLEAN DEFAULT false,
  continue_on_error BOOLEAN DEFAULT true,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'paused', 'completed', 'failed', 'cancelled')),
  total_files INTEGER DEFAULT 0,
  downloaded_files INTEGER DEFAULT 0,
  failed_files INTEGER DEFAULT 0,
  skipped_files INTEGER DEFAULT 0,
  total_size BIGINT DEFAULT 0,
  downloaded_size BIGINT DEFAULT 0,
  current_user_email TEXT, -- Email của user đang được download
  current_file_name TEXT, -- Tên file đang được download
  error_log JSONB DEFAULT '[]', -- Log các lỗi xảy ra
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Bảng osp_com_vn_download_items: Track từng file download
CREATE TABLE IF NOT EXISTS osp_com_vn_download_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  download_session_id UUID NOT NULL REFERENCES osp_com_vn_download_sessions(id) ON DELETE CASCADE,
  scanned_file_id BIGINT NOT NULL REFERENCES osp_com_vn_scanned_files(id),
  user_email TEXT NOT NULL,
  file_id TEXT NOT NULL, -- Google Drive file ID
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL, -- Path trong Google Drive
  local_path TEXT, -- Path sau khi download về local
  file_size BIGINT DEFAULT 0,
  mime_type TEXT,
  is_folder BOOLEAN DEFAULT FALSE,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'downloading', 'completed', 'failed', 'skipped')),
  retry_count INTEGER DEFAULT 0,
  error_message TEXT,
  web_view_link TEXT, -- Link để view file trên Google Drive
  export_links TEXT, -- JSON string of export links for Google Docs
  download_started_at TIMESTAMPTZ,
  download_completed_at TIMESTAMPTZ,
  download_duration INTEGER, -- milliseconds
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Update bảng osp_com_vn_scanned_files để thêm download tracking
ALTER TABLE osp_com_vn_scanned_files 
ADD COLUMN IF NOT EXISTS download_status TEXT CHECK (download_status IN ('not_downloaded', 'downloaded', 'failed', 'exposed')),
ADD COLUMN IF NOT EXISTS local_path TEXT,
ADD COLUMN IF NOT EXISTS downloaded_at TIMESTAMPTZ;

-- Indexes để tối ưu performance
CREATE INDEX IF NOT EXISTS idx_osp_com_vn_download_sessions_status ON osp_com_vn_download_sessions(status);
CREATE INDEX IF NOT EXISTS idx_osp_com_vn_download_sessions_created_at ON osp_com_vn_download_sessions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_osp_com_vn_download_items_session_id ON osp_com_vn_download_items(download_session_id);
CREATE INDEX IF NOT EXISTS idx_osp_com_vn_download_items_status ON osp_com_vn_download_items(status);
CREATE INDEX IF NOT EXISTS idx_osp_com_vn_download_items_user_email ON osp_com_vn_download_items(user_email);
CREATE INDEX IF NOT EXISTS idx_osp_com_vn_scanned_files_download_status ON osp_com_vn_scanned_files(download_status);

-- Trigger để auto-update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_osp_com_vn_download_sessions_updated_at 
    BEFORE UPDATE ON osp_com_vn_download_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- View để thống kê download sessions
CREATE OR REPLACE VIEW osp_com_vn_download_session_stats AS
SELECT 
    ds.*,
    CASE 
        WHEN ds.status = 'running' AND ds.started_at IS NOT NULL THEN 
            EXTRACT(EPOCH FROM (NOW() - ds.started_at))::INTEGER
        WHEN ds.status IN ('completed', 'failed', 'cancelled') AND ds.started_at IS NOT NULL AND ds.completed_at IS NOT NULL THEN
            EXTRACT(EPOCH FROM (ds.completed_at - ds.started_at))::INTEGER
        ELSE NULL
    END as duration_seconds
FROM osp_com_vn_download_sessions ds;

-- Comments
COMMENT ON TABLE osp_com_vn_download_sessions IS 'Quản lý các session download files từ Google Drive cho domain osp.com.vn';
COMMENT ON TABLE osp_com_vn_download_items IS 'Track chi tiết từng file trong session download cho domain osp.com.vn';
COMMENT ON COLUMN osp_com_vn_download_sessions.selected_users IS 'Array email của users được chọn để download';
COMMENT ON COLUMN osp_com_vn_download_sessions.download_path IS 'Đường dẫn local để lưu files';
COMMENT ON COLUMN osp_com_vn_download_sessions.concurrent_downloads IS 'Số lượng download đồng thời';
COMMENT ON COLUMN osp_com_vn_download_items.local_path IS 'Đường dẫn file sau khi download về local';
COMMENT ON COLUMN osp_com_vn_download_items.is_folder IS 'True nếu là folder, false nếu là file';
COMMENT ON COLUMN osp_com_vn_download_items.web_view_link IS 'Link để view file trên Google Drive (dành cho Google Docs)';
COMMENT ON COLUMN osp_com_vn_download_items.export_links IS 'JSON string chứa các export links cho Google Docs';
