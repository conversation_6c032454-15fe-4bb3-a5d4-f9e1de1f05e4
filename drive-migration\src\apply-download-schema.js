/**
 * Apply Download Schema
 * Script để áp dụng schema cho tính năng download files
 */

import fs from 'fs';
import path from 'path';
import { SupabaseClient } from './database/supabase.js';
import dotenv from 'dotenv';
import chalk from 'chalk';

dotenv.config();

// Khởi tạo Supabase client
const supabase = new SupabaseClient();

/**
 * Áp dụng schema SQL từ file
 */
async function applyDownloadSchema() {
    try {
        console.log(chalk.blue('🔄 Applying download schema...'));

        // Đọc file SQL
        const schemaPath = path.join(process.cwd(), 'database', 'download-schema.sql');
        const schemaSQL = fs.readFileSync(schemaPath, 'utf8');

        // Split SQL thành các statements riêng biệt
        const statements = schemaSQL
            .split(';')
            .map(statement => statement.trim())
            .filter(statement => statement.length > 0);

        console.log(chalk.yellow(`Found ${statements.length} SQL statements to execute`));

        // Thực hiện manual execution vì không có RPC function
        console.log(chalk.yellow('⚠️  Manual execution required. Please run the following SQL in Supabase dashboard:'));
        console.log(chalk.blue('='.repeat(80)));
        console.log(schemaSQL);
        console.log(chalk.blue('='.repeat(80)));

        // Thử verify tables để xem có tồn tại chưa
        console.log(chalk.yellow('Checking if tables already exist...'));

        // Verify tables exist
        await verifyTables();

        console.log(chalk.green('✅ Download schema applied successfully!'));
    } catch (error) {
        console.error(chalk.red('❌ Error applying download schema:'), error.message);
        process.exit(1);
    }
}

/**
 * Verify tables were created successfully
 */
async function verifyTables() {
    console.log(chalk.blue('🔍 Verifying tables...'));

    const tables = [
        process.env.DOWNLOAD_SESSIONS_TABLE_NAME || 'download_sessions',
        process.env.DOWNLOAD_ITEMS_TABLE_NAME || 'download_items'
    ];

    for (const table of tables) {
        const { data, error } = await supabase.getServiceClient()
            .from(table)
            .select('*')
            .limit(1);

        if (error && error.message.includes('does not exist')) {
            console.log(chalk.red(`❌ Table ${table} does not exist`));
            console.log(chalk.yellow('Please check the SQL execution logs above for errors'));
        } else if (error) {
            console.log(chalk.red(`❌ Error verifying table ${table}:`), error.message);
        } else {
            console.log(chalk.green(`✅ Table ${table} exists`));
        }
    }

    // Verify scanned_files table updates
    const { data, error } = await supabase.getServiceClient()
        .from(process.env.SCANNED_FILES_TABLE_NAME || 'scanned_files')
        .select('download_status, local_path')
        .limit(1);

    if (error && error.message.includes('column "download_status" does not exist')) {
        console.log(chalk.red('❌ scanned_files table was not updated correctly'));
    } else {
        console.log(chalk.green('✅ scanned_files table updated successfully'));
    }
}

// Run the script
applyDownloadSchema();
