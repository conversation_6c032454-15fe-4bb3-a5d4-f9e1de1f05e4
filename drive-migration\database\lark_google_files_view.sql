-- View đ<PERSON> kết hợp thông tin Lark files v<PERSON>i Google Drive permissions
-- <PERSON><PERSON><PERSON>u kiện match: user_email, name, full_path giống nhau
-- PostgreSQL syntax

-- Main view combining Lark and Google Drive files
CREATE OR REPLACE VIEW lark_google_files_with_permissions AS
SELECT 
    -- Lark file information (using original column names)
    lf.id,
    lf.token,
    lf.name,
    lf.full_path,
    lf.type,
    lf.parent_token,
    lf.url,
    lf.user_email,
    lf.synced,
    lf.scanned,
    lf.created_at,
    lf.updated_at,
    
    -- Google Drive permissions only
    gf.permissions as google_permissions,
    
    -- Match status and information
    CASE 
        WHEN gf.id IS NOT NULL THEN 'matched'
        ELSE 'lark_only'
    END as match_status
    
FROM scanned_lark_files lf
LEFT JOIN scanned_files gf ON (
    lf.user_email = gf.user_email 
    AND lf.name = gf.name 
    AND lf.full_path = gf.full_path
    AND lf.type != 'folder'  -- Exclude folders from matching
    AND gf.mime_type != 'application/vnd.google-apps.folder'  -- Exclude Google Drive folders
)
WHERE lf.type != 'folder'  -- Only include files, not folders
ORDER BY lf.user_email, lf.full_path, lf.name;

-- Create index on the view for better performance
CREATE INDEX IF NOT EXISTS idx_lark_google_view_user_email 
ON scanned_lark_files(user_email, name, full_path) 
WHERE type != 'folder';

CREATE INDEX IF NOT EXISTS idx_google_files_match_key
ON scanned_files(user_email, name, full_path)
WHERE mime_type != 'application/vnd.google-apps.folder';

-- View for matched files only (files that exist in both Lark and Google Drive)
CREATE OR REPLACE VIEW matched_lark_google_files AS
SELECT *
FROM lark_google_files_with_permissions
WHERE match_status = 'matched';

-- View for statistics by user
CREATE OR REPLACE VIEW lark_google_sync_stats AS
SELECT 
    user_email,
    COUNT(*) as total_lark_files,
    COUNT(CASE WHEN match_status = 'matched' THEN 1 END) as matched_files,
    COUNT(CASE WHEN match_status = 'lark_only' THEN 1 END) as lark_only_files,
    ROUND(
        CASE 
            WHEN COUNT(*) > 0 THEN 
                (COUNT(CASE WHEN match_status = 'matched' THEN 1 END)::DECIMAL / COUNT(*)) * 100 
            ELSE 0 
        END, 2
    ) as match_percentage
FROM lark_google_files_with_permissions
GROUP BY user_email
ORDER BY user_email;

-- Comments for documentation
COMMENT ON VIEW lark_google_files_with_permissions IS 'Kết hợp thông tin từ Lark files và Google Drive files dựa trên email, name, full_path';
COMMENT ON VIEW matched_lark_google_files IS 'Chỉ các file tồn tại ở cả Lark và Google Drive';
COMMENT ON VIEW lark_google_sync_stats IS 'Thống kê tình trạng đồng bộ Lark-Google files theo user';
