import { supabaseClient } from '../database/supabase.js';
import { larkAuth } from '../auth/lark-auth.js';

/**
 * User Mapping Service
 * Hệ thống mapping người dùng Google Drive sang Lark Drive với:
 * - Auto-detection từ email domains
 * - Manual mapping interface
 * - Validation và conflict resolution
 * - Bulk import/export capabilities
 * - Permission inheritance mapping
 */
export class UserMappingService {
    constructor() {
        this.larkAuth = larkAuth;

        // Configuration
        this.config = {
            autoMappingRules: [
                // Rule: Same email domain
                { type: 'same_domain', priority: 1 },
                // Rule: Email prefix matching
                { type: 'email_prefix', priority: 2 },
                // Rule: Name similarity
                { type: 'name_similarity', priority: 3 }
            ],
            similarityThreshold: 0.8, // For name matching
            batchSize: 100 // For bulk operations
        };

        // Statistics tracking
        this.stats = {
            totalUsers: 0,
            mappedUsers: 0,
            unmappedUsers: 0,
            autoMappedUsers: 0,
            manualMappedUsers: 0,
            conflictUsers: 0
        };

        // Cache for Lark users
        this.larkUsersCache = new Map();
        this.cacheExpiry = null;
    }

    /**
     * Initialize user mapping từ Google Drive permissions
     * @param {Array} drivePermissions - Array of Google Drive permissions
     * @returns {Promise<object>} Initialization result
     */
    async initializeUserMapping(drivePermissions) {
        try {
            console.log(`👥 Initializing user mapping from ${drivePermissions.length} permissions`);

            // Extract unique users from permissions
            const uniqueUsers = this.extractUniqueUsers(drivePermissions);
            console.log(`📊 Found ${uniqueUsers.length} unique users`);

            // Get existing mappings
            const existingMappings = await this.getExistingMappings();
            const existingEmails = new Set(existingMappings.map(m => m.email_google));

            // Filter new users
            const newUsers = uniqueUsers.filter(user => !existingEmails.has(user.email));

            if (newUsers.length === 0) {
                console.log('✅ All users already exist in mapping table');
                return {
                    success: true,
                    totalUsers: uniqueUsers.length,
                    newUsers: 0,
                    existingUsers: existingMappings.length
                };
            }

            // Insert new users
            const insertResult = await this.insertNewUsers(newUsers);

            // Attempt auto-mapping
            const autoMappingResult = await this.performAutoMapping(newUsers);

            // Update statistics
            await this.updateStatistics();

            console.log(`✅ User mapping initialized: ${insertResult.insertedCount} new users, ${autoMappingResult.autoMappedCount} auto-mapped`);

            return {
                success: true,
                totalUsers: uniqueUsers.length,
                newUsers: insertResult.insertedCount,
                existingUsers: existingMappings.length,
                autoMappedCount: autoMappingResult.autoMappedCount,
                unmappedCount: newUsers.length - autoMappingResult.autoMappedCount
            };

        } catch (error) {
            console.error('❌ Error initializing user mapping:', error.message);
            throw new Error(`Failed to initialize user mapping: ${error.message}`);
        }
    }

    /**
     * Extract unique users from Drive permissions
     * @param {Array} permissions - Drive permissions array
     * @returns {Array} Unique users with metadata
     */
    extractUniqueUsers(permissions) {
        const userMap = new Map();

        for (const permission of permissions) {
            if (permission.type === 'user' && permission.emailAddress) {
                const email = permission.emailAddress.toLowerCase();

                if (!userMap.has(email)) {
                    userMap.set(email, {
                        email,
                        displayName: permission.displayName || '',
                        role: permission.role || 'reader',
                        domain: email.split('@')[1] || '',
                        firstSeen: new Date().toISOString()
                    });
                }
            }
        }

        return Array.from(userMap.values());
    }

    /**
     * Get existing user mappings
     * @returns {Promise<Array>} Existing mappings
     */
    async getExistingMappings() {
        try {
            const { data, error } = await supabaseClient.getServiceClient()
                .from('users')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) {
                throw new Error(`Database error: ${error.message}`);
            }

            return data || [];
        } catch (error) {
            console.error('❌ Error getting existing mappings:', error.message);
            throw error;
        }
    }

    /**
     * Insert new users into mapping table
     * @param {Array} newUsers - New users to insert
     * @returns {Promise<object>} Insert result
     */
    async insertNewUsers(newUsers) {
        try {
            const usersToInsert = newUsers.map(user => ({
                email_google: user.email,
                lark_userid: null,
                mapped: false,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }));

            const { data, error } = await supabaseClient.getServiceClient()
                .from(process.env.USERS_TABLE_NAME || 'users')
                .insert(usersToInsert)
                .select();

            if (error) {
                throw new Error(`Database error: ${error.message}`);
            }

            return {
                insertedCount: data?.length || 0,
                insertedUsers: data || []
            };
        } catch (error) {
            console.error('❌ Error inserting new users:', error.message);
            throw error;
        }
    }

    /**
     * Perform automatic user mapping
     * @param {Array} users - Users to auto-map
     * @returns {Promise<object>} Auto-mapping result
     */
    async performAutoMapping(users) {
        try {
            console.log(`🤖 Performing auto-mapping for ${users.length} users`);

            // Get Lark users for comparison
            const larkUsers = await this.getLarkUsers();
            let autoMappedCount = 0;
            const mappingResults = [];

            for (const user of users) {
                const mapping = await this.findBestLarkMatch(user, larkUsers);

                if (mapping && mapping.confidence >= this.config.similarityThreshold) {
                    // Update mapping in database
                    const updateResult = await this.updateUserMapping(
                        user.email,
                        mapping.larkUserId,
                        true,
                        `Auto-mapped: ${mapping.method} (confidence: ${mapping.confidence})`
                    );

                    if (updateResult.success) {
                        autoMappedCount++;
                        mappingResults.push({
                            googleEmail: user.email,
                            larkUserId: mapping.larkUserId,
                            method: mapping.method,
                            confidence: mapping.confidence
                        });

                        console.log(`✅ Auto-mapped: ${user.email} -> ${mapping.larkUserId} (${mapping.method})`);
                    }
                }
            }

            return {
                autoMappedCount,
                mappingResults
            };
        } catch (error) {
            console.error('❌ Error in auto-mapping:', error.message);
            return { autoMappedCount: 0, mappingResults: [] };
        }
    }

    /**
     * Get Lark users với caching
     * @returns {Promise<Array>} Lark users list
     */
    async getLarkUsers() {
        try {
            // Check cache
            if (this.larkUsersCache.size > 0 && this.cacheExpiry && Date.now() < this.cacheExpiry) {
                return Array.from(this.larkUsersCache.values());
            }

            console.log('📞 Fetching Lark users...');

            // Mock Lark users for now (sẽ implement real API call sau)
            const mockLarkUsers = [
                {
                    user_id: 'ou_123456789',
                    name: 'John Doe',
                    email: '<EMAIL>',
                    department: 'Engineering',
                    status: 'active'
                },
                {
                    user_id: 'ou_987654321',
                    name: 'Jane Smith',
                    email: '<EMAIL>',
                    department: 'Marketing',
                    status: 'active'
                },
                {
                    user_id: 'ou_456789123',
                    name: 'Bob Johnson',
                    email: '<EMAIL>',
                    department: 'Sales',
                    status: 'active'
                }
            ];

            // Cache users
            this.larkUsersCache.clear();
            for (const user of mockLarkUsers) {
                this.larkUsersCache.set(user.user_id, user);
            }
            this.cacheExpiry = Date.now() + (60 * 60 * 1000); // 1 hour cache

            console.log(`✅ Fetched ${mockLarkUsers.length} Lark users`);
            return mockLarkUsers;

        } catch (error) {
            console.error('❌ Error fetching Lark users:', error.message);
            return [];
        }
    }

    /**
     * Find best Lark user match for Google user
     * @param {object} googleUser - Google user info
     * @param {Array} larkUsers - Lark users list
     * @returns {object|null} Best match with confidence score
     */
    findBestLarkMatch(googleUser, larkUsers) {
        let bestMatch = null;
        let highestConfidence = 0;

        for (const larkUser of larkUsers) {
            // Rule 1: Exact email match
            if (larkUser.email && larkUser.email.toLowerCase() === googleUser.email.toLowerCase()) {
                return {
                    larkUserId: larkUser.user_id,
                    method: 'exact_email',
                    confidence: 1.0
                };
            }

            // Rule 2: Same domain + email prefix match
            const googlePrefix = googleUser.email.split('@')[0];
            const larkPrefix = larkUser.email ? larkUser.email.split('@')[0] : '';
            const googleDomain = googleUser.email.split('@')[1];
            const larkDomain = larkUser.email ? larkUser.email.split('@')[1] : '';

            if (googleDomain === larkDomain && googlePrefix === larkPrefix) {
                const confidence = 0.9;
                if (confidence > highestConfidence) {
                    bestMatch = {
                        larkUserId: larkUser.user_id,
                        method: 'same_domain_prefix',
                        confidence
                    };
                    highestConfidence = confidence;
                }
            }

            // Rule 3: Name similarity (if available)
            if (googleUser.displayName && larkUser.name) {
                const nameSimilarity = this.calculateStringSimilarity(
                    googleUser.displayName.toLowerCase(),
                    larkUser.name.toLowerCase()
                );

                if (nameSimilarity > highestConfidence && nameSimilarity >= 0.7) {
                    bestMatch = {
                        larkUserId: larkUser.user_id,
                        method: 'name_similarity',
                        confidence: nameSimilarity
                    };
                    highestConfidence = nameSimilarity;
                }
            }
        }

        return bestMatch;
    }

    /**
     * Calculate string similarity using Levenshtein distance
     * @param {string} str1 - First string
     * @param {string} str2 - Second string
     * @returns {number} Similarity score (0-1)
     */
    calculateStringSimilarity(str1, str2) {
        const maxLength = Math.max(str1.length, str2.length);
        if (maxLength === 0) return 1;

        const distance = this.levenshteinDistance(str1, str2);
        return (maxLength - distance) / maxLength;
    }

    /**
     * Calculate Levenshtein distance
     * @param {string} str1 - First string
     * @param {string} str2 - Second string
     * @returns {number} Edit distance
     */
    levenshteinDistance(str1, str2) {
        const matrix = [];

        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }

        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }

        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }

        return matrix[str2.length][str1.length];
    }

    /**
     * Update user mapping
     * @param {string} googleEmail - Google email
     * @param {string} larkUserId - Lark user ID
     * @param {boolean} mapped - Is mapped
     * @param {string} notes - Mapping notes
     * @returns {Promise<object>} Update result
     */
    async updateUserMapping(googleEmail, larkUserId, mapped = true, notes = '') {
        try {
            const { data, error } = await supabaseClient.getServiceClient()
                .from('users')
                .update({
                    lark_userid: larkUserId,
                    mapped,
                    updated_at: new Date().toISOString(),
                    notes
                })
                .eq('email_google', googleEmail)
                .select();

            if (error) {
                throw new Error(`Database error: ${error.message}`);
            }

            return {
                success: true,
                updatedUser: data?.[0] || null
            };
        } catch (error) {
            console.error(`❌ Error updating user mapping for ${googleEmail}:`, error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Get user mapping statistics
     * @returns {Promise<object>} Statistics
     */
    async updateStatistics() {
        try {
            const { data, error } = await supabaseClient.getServiceClient()
                .from('users')
                .select('mapped');

            if (error) {
                throw new Error(`Database error: ${error.message}`);
            }

            const total = data?.length || 0;
            const mapped = data?.filter(u => u.mapped).length || 0;
            const unmapped = total - mapped;

            this.stats = {
                totalUsers: total,
                mappedUsers: mapped,
                unmappedUsers: unmapped,
                mappingRate: total > 0 ? (mapped / total) * 100 : 0
            };

            return this.stats;
        } catch (error) {
            console.error('❌ Error updating statistics:', error.message);
            return this.stats;
        }
    }

    /**
     * Get current statistics
     * @returns {object} Current statistics
     */
    getStats() {
        return this.stats;
    }

    /**
     * Clear Lark users cache
     */
    clearCache() {
        this.larkUsersCache.clear();
        this.cacheExpiry = null;
        console.log('🗑️ User mapping cache cleared');
    }
}

// Export singleton instance
export const userMappingService = new UserMappingService();
