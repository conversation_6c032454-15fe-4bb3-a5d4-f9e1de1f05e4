import { larkUploadEngine } from './lark-upload-engine.js';
import { userMappingService } from './user-mapping-service.js';
import { realtimeService } from './realtime-service.js';
import { supabaseClient } from '../database/supabase.js';
import { googleDriveAPI } from '../api/google-drive-api.js';
import { larkDriveAPI } from '../api/lark-drive-api.js';
import path from 'path';
import fs from 'fs';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Migration Engine
 * Core orchestration engine cho toàn bộ quá trình migration từ local files sang Lark Drive
 * Bao gồm:
 * - File reading từ local storage
 * - File upload lên Lark Drive
 * - Permission mapping và assignment
 * - Progress tracking và error handling
 * - Resume capability và checkpoint system
 */
export class MigrationEngine {
    constructor() {
        this.uploadEngine = larkUploadEngine;
        this.userMapping = userMappingService;
        this.realtime = realtimeService;
        this.googleAPI = googleDriveAPI;
        this.larkAPI = larkDriveAPI;
        this.storagePath = process.env.LARK_STORAGE_PATH;

        // Development mode configuration
        this.isDevelopment = process.env.NODE_ENV === 'development';
        this.mockFilePath = process.env.LARK_MOCK_FILE;

        // Domain validation configuration
        this.allowedDomain = process.env.GOOGLE_DOMAIN;

        // Configuration
        this.config = {
            maxConcurrentMigrations: 3,
            maxRetries: 3,
            retryDelay: 5000, // 5 seconds
            checkpointInterval: 10, // Save checkpoint every 10 files
            tempDir: './temp/migration',
            preserveFolderStructure: true
        };

        // Migration state tracking
        this.activeMigrations = new Map();
        this.migrationQueue = [];

        // Statistics
        this.stats = {
            totalMigrations: 0,
            successfulMigrations: 0,
            failedMigrations: 0,
            totalFiles: 0,
            processedFiles: 0,
            totalBytes: 0,
            processedBytes: 0,
            averageSpeed: 0,
            estimatedTimeRemaining: 0,
            errors: []
        };

        // Ensure temp directory exists
        this.ensureTempDirectory();

        // Google to Lark permission mapping
        this.mapPermissions = {
            'owner': 'full_access',
            'organizer': 'full_access',
            'fileOrganizer': 'full_access',
            'writer': 'edit',
            'commenter': 'view',
            'reader': 'view'
        }
    }

    /**
     * Ensure temp directory exists
     */
    ensureTempDirectory() {
        if (!fs.existsSync(this.config.tempDir)) {
            fs.mkdirSync(this.config.tempDir, { recursive: true });
            console.log(`📁 Created migration temp directory: ${this.config.tempDir}`);
        }
    }

    /**
     * Validate email address domain against allowed domain
     * @param {string} emailAddress - Email address to validate
     * @returns {object} Validation result with isValid flag and reason
     */
    validateEmailDomain(emailAddress) {
        try {
            // Check if emailAddress is provided
            if (!emailAddress || typeof emailAddress !== 'string') {
                return {
                    isValid: false,
                    reason: 'Email address is required and must be a string'
                };
            }

            // Check if email format is valid
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(emailAddress)) {
                return {
                    isValid: false,
                    reason: 'Invalid email format'
                };
            }

            // Check if allowed domain is configured
            if (!this.allowedDomain) {
                console.warn('⚠️ GOOGLE_DOMAIN not configured, skipping domain validation');
                return {
                    isValid: true,
                    reason: 'Domain validation skipped - GOOGLE_DOMAIN not configured'
                };
            }

            // Extract domain from email
            const emailDomain = emailAddress.split('@')[1];

            // Check if domain matches allowed domain
            if (emailDomain.toLowerCase() === this.allowedDomain.toLowerCase()) {
                return {
                    isValid: true,
                    reason: `Email domain matches allowed domain: ${this.allowedDomain}`
                };
            } else {
                return {
                    isValid: false,
                    reason: `Email domain '${emailDomain}' does not match allowed domain '${this.allowedDomain}'`
                };
            }

        } catch (error) {
            console.error('❌ Error validating email domain:', error.message);
            return {
                isValid: false,
                reason: `Domain validation error: ${error.message}`
            };
        }
    }

    /**
     * Start migration process cho selected files
     * @param {string} userEmail - Google user email
     * @param {string} sessionId - Scan session ID
     * @param {object} options - Migration options
     * @param {function} progressCallback - Progress callback function
     * @returns {Promise<object>} Migration result
     */
    async startMigration(migrationId, userEmail, sessionId, options = {}, progressCallback = null) {
        try {
            console.log(`🚀 Starting migration: ${migrationId}`);

            // Initialize migration record
            const migration = await this.initializeMigration(migrationId, userEmail, sessionId, options);
            this.activeMigrations.set(migrationId, migration);

            // Create realtime channel for progress updates
            this.realtime.createMigrationChannel(migrationId);

            // Broadcast migration started
            await this.realtime.broadcastStatusChange(migrationId, 'running', {
                previousStatus: 'pending',
                reason: 'Migration started'
            });

            // Get selected files for migration
            const selectedFiles = await this.getSelectedFiles(sessionId);
            if (selectedFiles.length === 0) {
                throw new Error('No files selected for migration');
            }

            console.log(`📊 Migration scope: ${selectedFiles.length} files`);

            // Update migration with file count
            await this.updateMigrationProgress(migrationId, {
                total_files: selectedFiles.length,
                total_size: selectedFiles.reduce((sum, file) => sum + (parseInt(file.size) || 0), 0)
            });

            // Initialize user mapping if needed
            if (options.mapPermissions) {
                await this.initializeUserMappingForMigration(selectedFiles);
            }

            // Create folder structure mapping
            const userFolder = userEmail; // Mỗi người đều có thư mục riêng
            const folderMapping = await this.createFolderStructureMapping(selectedFiles, userFolder);

            // Process files in batches
            const results = await this.processMigrationBatch(
                migrationId,
                userEmail,
                selectedFiles,
                folderMapping,
                options,
                progressCallback
            );

            // Finalize migration
            const finalResult = await this.finalizeMigration(migrationId, results);

            // Broadcast migration completion
            await this.realtime.broadcastMigrationComplete(migrationId, finalResult);

            console.log(`✅ Migration completed: ${migrationId}`);
            return finalResult;

        } catch (error) {
            console.error(`❌ Migration failed: ${migrationId} - ${error.message}`);

            // Broadcast error
            await this.realtime.broadcastError(migrationId, {
                severity: 'critical',
                errorMessage: error.message,
                retryable: false
            });

            await this.handleMigrationError(migrationId, error);
            throw error;
        } finally {
            this.activeMigrations.delete(migrationId);
        }
    }

    /**
     * Initialize migration record in database
     * @param {string} migrationId - Migration ID
     * @param {string} userEmail - User email
     * @param {string} sessionId - Session ID
     * @param {object} options - Migration options
     * @returns {Promise<object>} Migration record
     */
    async initializeMigration(migrationId, userEmail, sessionId, options) {
        try {
            const migrationData = {
                id: migrationId,
                owner_email: userEmail,
                scan_session_id: sessionId,
                status: 'running',
                options: options,
                total_files: 0,
                processed_files: 0,
                successful_files: 0,
                failed_files: 0,
                total_size: 0,
                processed_size: 0,
                started_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            };

            const { data, error } = await supabaseClient.getServiceClient()
                .from('migration_tasks')
                .insert(migrationData)
                .select()
                .single();

            if (error) {
                throw new Error(`Database error: ${error.message}`);
            }

            return data;
        } catch (error) {
            console.error('❌ Error initializing migration:', error.message);
            throw error;
        }
    }

    /**
     * Get selected files for migration
     * @param {string} sessionId - Scan session ID
     * @returns {Promise<Array>} Selected files
     */
    async getSelectedFiles(sessionId) {
        try {
            // Fetch all selected files in batches to handle large datasets
            let allFiles = [];
            let hasMore = true;
            let offset = 0;
            const batchSize = 1000; // Process in batches of 1000

            while (hasMore) {
                const { data: batchFiles, error } = await supabaseClient
                    .from(process.env.SCANNED_FILES_TABLE_NAME || 'scanned_files')
                    .select('*')
                    .eq('scan_session_id', sessionId)
                    .eq('is_selected', true)
                    .order('full_path', { ascending: true })
                    .range(offset, offset + batchSize - 1);

                if (error) {
                    throw new Error(`Database error: ${error.message}`);
                }

                if (batchFiles && batchFiles.length > 0) {
                    allFiles.push(...batchFiles);

                    // Check if we got a full batch, indicating there might be more
                    hasMore = batchFiles.length === batchSize;
                    offset += batchSize;

                    console.log(`📄 Fetched selected files batch: ${batchFiles.length} files (Total so far: ${allFiles.length})`);
                } else {
                    hasMore = false;
                }
            }

            console.log(`✅ Total selected files: ${allFiles.length}`);
            return allFiles;
        } catch (error) {
            console.error('❌ Error getting selected files:', error.message);
            throw error;
        }
    }

    /**
     * Initialize user mapping for migration
     * @param {Array} files - Files to migrate
     */
    async initializeUserMappingForMigration(files) {
        try {
            console.log('👥 Initializing user mapping for migration...');

            // Extract all permissions from files
            const allPermissions = [];
            for (const file of files) {
                if (file.permissions && Array.isArray(file.permissions)) {
                    allPermissions.push(...file.permissions);
                }
            }

            if (allPermissions.length > 0) {
                await this.userMapping.initializeUserMapping(allPermissions);
                console.log('✅ User mapping initialized for migration');
            }
        } catch (error) {
            console.warn('⚠️ User mapping initialization failed:', error.message);
            // Don't fail migration if user mapping fails
        }
    }

    /**
     * Create folder structure mapping
     * @param {Array} files - Files to migrate
     * @param {string} userFolder - Target user folder in Lark
     * @returns {Promise<Map>} Folder mapping (Google path -> Lark folder ID)
     */
    async createFolderStructureMapping(files, userFolder) {
        try {
            console.log('📁 Creating folder structure mapping...');

            const folderMapping = new Map();
            const uniqueFolders = new Set();

            // Extract unique folder paths
            for (const file of files) {
                const folderPath = path.dirname(file.full_path);
                if (folderPath && folderPath !== '.' && folderPath !== '/') {
                    uniqueFolders.add(folderPath);
                }
            }

            // Sort folders by depth (shallow first)
            let sortedFolders = Array.from(uniqueFolders).sort((a, b) => {
                return a.split('/').length - b.split('/').length;
            });

            // Prepend user folder to each folder path
            sortedFolders = sortedFolders.map(folder => `${userFolder}${folder}`);
            sortedFolders = [userFolder, ...sortedFolders]; // Ensure user folder is first

            // Create folders in Lark Drive
            for (const folderPath of sortedFolders) {
                try {
                    const larkFolder = await this.uploadEngine.createFolderStructure(folderPath);

                    folderMapping.set(folderPath, larkFolder.token);
                    console.log(`✅ Mapped folder: ${folderPath} -> ${larkFolder.token}`);
                } catch (error) {
                    console.error(`❌ Failed to create folder ${folderPath}:`, error.message);
                    // Continue with other folders
                }
            }

            return folderMapping;
        } catch (error) {
            console.error('❌ Error creating folder structure mapping:', error.message);
            return new Map();
        }
    }

    /**
     * Process migration batch
     * @param {string} migrationId - Migration ID
     * @param {string} userEmail - User email
     * @param {Array} files - Files to migrate
     * @param {Map} folderMapping - Folder mapping
     * @param {object} options - Migration options
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<Array>} Migration results
     */
    async processMigrationBatch(migrationId, userEmail, files, folderMapping, options, progressCallback) {
        const results = [];
        let processedCount = 0;
        let successCount = 0;
        let failureCount = 0;

        console.log(`📦 Processing ${files.length} files in batches...`);

        // Process files in smaller batches to manage memory
        const batchSize = this.config.maxConcurrentMigrations;

        // TODO filter files
        files = files.filter(file => file.mime_type !== 'application/vnd.google-apps.folder'); // Filter out invalid files

        for (let i = 0; i < files.length; i += batchSize) {
            const batch = files.slice(i, i + batchSize);

            const batchPromises = batch.map(async (file) => {
                return await this.migrateFile(
                    migrationId,
                    userEmail,
                    file,
                    folderMapping,
                    options,
                    (fileProgress) => {
                        if (progressCallback) {
                            progressCallback({
                                type: 'file_progress',
                                migrationId,
                                fileId: file.file_id,
                                fileName: file.name,
                                ...fileProgress
                            });
                        }
                    }
                );
            });

            const batchResults = await Promise.allSettled(batchPromises);

            // Process batch results
            for (const result of batchResults) {
                processedCount++;

                if (result.status === 'fulfilled' && result.value.success) {
                    successCount++;
                    results.push(result.value);
                } else {
                    failureCount++;
                    const error = result.status === 'rejected' ? result.reason : result.value.error;
                    results.push({
                        success: false,
                        error: error?.message || 'Unknown error',
                        fileId: batch[batchResults.indexOf(result)]?.file_id
                    });
                }

                // Update progress
                const progressData = {
                    type: 'batch_progress',
                    migrationId,
                    processedFiles: processedCount,
                    totalFiles: files.length,
                    successfulFiles: successCount,
                    failedFiles: failureCount,
                    progress: (processedCount / files.length) * 100
                };

                if (progressCallback) {
                    progressCallback(progressData);
                }

                // Broadcast real-time progress
                await this.realtime.broadcastBatchProgress(migrationId, progressData);
            }

            // Update migration progress in database
            await this.updateMigrationProgress(migrationId, {
                processed_files: processedCount,
                successful_files: successCount,
                failed_files: failureCount
            });

            // Save checkpoint
            if (processedCount % this.config.checkpointInterval === 0) {
                await this.saveCheckpoint(migrationId, processedCount, results);
            }

            // Small delay between batches
            if (i + batchSize < files.length) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        return results;
    }

    /**
     * Migrate single file from local storage to Lark Drive
     * @param {string} migrationId - Migration ID
     * @param {string} userEmail - User email
     * @param {object} file - File to migrate 
     * @param {Map} folderMapping - Folder mapping
     * @param {object} options - Migration options
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<object>} Migration result
     */
    async migrateFile(migrationId, userEmail, file, folderMapping, options, progressCallback) {
        try {
            console.log(`📄 Migrating file: ${file.name} (${file.file_id})`);

            // Step 1: Verify local file exists and get file info
            const userFolder = userEmail; // Mỗi người đều có thư mục riêng
            let filePath = `${this.storagePath}/${userFolder}${file.full_path}`;

            if (!filePath) {
                throw new Error('No local file path provided');
            }

            // Check if in development mode and use mock file
            let actualFilePath = filePath;
            if (this.isDevelopment && this.mockFilePath) {
                actualFilePath = this.mockFilePath;
                console.log(`🔧 Development mode: Using mock file ${actualFilePath} instead of ${filePath}`);
            } else if (this.isDevelopment) {
                console.warn(`⚠️ Development mode enabled but LARK_MOCK_FILE not configured in .env`);
            }

            if (!fs.existsSync(actualFilePath)) {
                throw new Error(`Local file not found: ${actualFilePath}`);
            }

            // Get file stats for progress tracking
            const fileStats = fs.statSync(actualFilePath);
            const fileSize = fileStats.size;

            console.log(`📂 Reading local file: ${actualFilePath} (${fileSize} bytes)`);

            // Report reading progress
            if (progressCallback) {
                const fileProgressData = {
                    phase: 'reading',
                    progress: 100,
                    bytesRead: fileSize,
                    totalBytes: fileSize,
                    fileName: file.name
                };
                progressCallback(fileProgressData);

                // Broadcast real-time file progress
                await this.realtime.broadcastFileProgress(migrationId, fileProgressData);
            }

            // Step 2: Determine target folder in Lark
            const targetFolderId = this.getTargetFolderId(file, folderMapping, userFolder);

            // Step 3: Upload file to Lark Drive
            const uploadResult = await this.uploadEngine.uploadFile(
                actualFilePath,
                file.name,
                targetFolderId,
                async (progress) => {
                    const fileProgressData = {
                        phase: 'uploading',
                        ...progress
                    };

                    if (progressCallback) {
                        progressCallback(fileProgressData);
                    }

                    // Broadcast real-time file progress
                    await this.realtime.broadcastFileProgress(migrationId, fileProgressData);
                }
            );

            if (!uploadResult.success) {
                throw new Error(`Upload failed: ${uploadResult.error}`);
            }

            // Step 4: Set permissions if enabled
            if (file.permissions) {
                await this.setFilePermissions(uploadResult.fileToken, file.permissions);
            }

            // Step 5: Record migration item
            await this.recordMigrationItem(migrationId, file, uploadResult);

            return {
                success: true,
                fileId: file.file_id,
                fileName: file.name,
                larkFileToken: uploadResult.fileToken,
                uploadResult
            };

        } catch (error) {
            console.error(`❌ File migration failed: ${file.name} - ${error.message}`);

            // Broadcast file error
            await this.realtime.broadcastError(migrationId, {
                severity: 'error',
                fileId: file.file_id,
                fileName: file.name,
                errorMessage: error.message,
                retryable: true
            });

            // Record failed migration
            await this.recordMigrationItem(migrationId, file, null, error.message);

            return {
                success: false,
                fileId: file.file_id,
                fileName: file.name,
                error: error.message
            };
        }
    }

    /**
     * Get target folder ID in Lark for a file
     * @param {object} file - File object
     * @param {Map} folderMapping - Folder mapping
     * @returns {string|null} Target folder ID
     */
    getTargetFolderId(file, folderMapping, userFolder) {
        let folderPath = path.dirname(`${userFolder}${file.full_path}`);

        if (folderPath && folderPath !== '.' && folderPath !== '/') {
            return folderMapping.get(folderPath) || null;
        }

        return null; // Root folder
    }

    /**
     * Set file permissions in Lark Drive
     * @param {string} fileToken - Lark file token
     * @param {Array} googlePermissions - Google Drive permissions
     * @returns {Promise<Array>} Permission setting results
     */
    async setFilePermissions(fileToken, googlePermissions) {
        try {
            console.log(`🔐 Setting permissions for file: ${fileToken} (${googlePermissions.length} permissions)`);

            const batchPromises = googlePermissions.map(async (permission, index) => {
                try {
                    let { role, type, emailAddress } = permission;

                    console.log(`🔐 Setting permission ${index + 1}/${googlePermissions.length}: ${type} - ${role} - ${emailAddress || 'N/A'}`);

                    if ((type === 'user' || type === 'group') && emailAddress) {
                        // Validate email domain before setting permissions
                        const domainValidation = this.validateEmailDomain(emailAddress);
                        if (!domainValidation.isValid) {
                            const skipMessage = `Domain validation failed for ${emailAddress}: ${domainValidation.reason}`;
                            console.warn(`⚠️ ${skipMessage} - Skipping permission`);
                            return {
                                skip: true,
                                message: skipMessage,
                                emailAddress: emailAddress,
                                type: type,
                                role: role
                            };
                        }

                        console.log(`✅ Domain validation passed for ${emailAddress}: ${domainValidation.reason}`);

                        if (role === 'owner') {
                            return await this.larkAPI.transferOwnership(fileToken, emailAddress);
                        }
                        else {
                            return await this.larkAPI.addPermissions(fileToken, {
                                "member_type": "email",
                                "member_id": emailAddress,
                                "perm": this.mapPermissions[role] || 'view'
                            });
                        }
                    }

                    if (type === 'anyone') {
                        return await this.larkAPI.setPermissions(fileToken, {
                            "perm": this.mapPermissions[role] || 'view'
                        });
                    }

                    // Unsupported permission type should be skipped instead of throwing error
                    const skipMessage = `Unsupported permission type: ${type} role: ${role} emailAddress: ${emailAddress}`;
                    console.warn(`⚠️ ${skipMessage} - Skipping permission`);
                    return {
                        skip: true,
                        message: skipMessage,
                        emailAddress: emailAddress,
                        type: type,
                        role: role
                    };

                } catch (permError) {
                    console.error(`❌ Failed to set individual permission ${index + 1}: ${permError.message}`);
                    throw permError;
                }
            });

            const batchResults = await Promise.allSettled(batchPromises);

            // Log detailed results
            const successful = batchResults.filter(r => r.status === 'fulfilled' && r.value && !r.value.skip).length;
            const skipped = batchResults.filter(r => r.status === 'fulfilled' && r.value && r.value.skip).length;
            const failed = batchResults.filter(r => r.status === 'rejected').length;

            console.log(`🔐 Permission setting completed for ${fileToken}: ${successful} successful, ${skipped} skipped, ${failed} failed`);

            return batchResults;

        } catch (error) {
            console.error(`❌ Failed to set permissions for ${fileToken}:`, error.message);
            // Return error result instead of undefined
            return [{
                status: 'rejected',
                reason: error
            }];
        }
    }

    /**
     * Update migration progress in database
     * @param {string} migrationId - Migration ID
     * @param {object} updates - Updates to apply
     */
    async updateMigrationProgress(migrationId, updates) {
        try {
            const { error } = await supabaseClient.getServiceClient()
                .from('migration_tasks')
                .update({
                    ...updates,
                    updated_at: new Date().toISOString()
                })
                .eq('id', migrationId);

            if (error) {
                console.error('❌ Error updating migration progress:', error.message);
            }
        } catch (error) {
            console.error('❌ Error updating migration progress:', error.message);
        }
    }

    /**
     * Record migration item in database
     * @param {string} migrationId - Migration ID
     * @param {object} file - File object
     * @param {object} uploadResult - Upload result
     * @param {string} errorMessage - Error message if failed
     */
    async recordMigrationItem(migrationId, file, uploadResult, errorMessage = null) {
        try {
            const itemData = {
                migration_task_id: migrationId,
                google_file_id: file.file_id,
                google_file_name: file.name,
                google_file_path: file.full_path,
                google_file_size: parseInt(file.size) || 0,
                lark_file_token: uploadResult?.fileToken || null,
                lark_file_name: uploadResult?.fileName || null,
                status: errorMessage ? 'failed' : 'completed',
                error_message: errorMessage,
                upload_time: uploadResult?.uploadTime || null,
                created_at: new Date().toISOString()
            };

            const { error } = await supabaseClient.getServiceClient()
                .from('migration_items')
                .insert(itemData);

            if (error) {
                console.error('❌ Error recording migration item:', error.message);
            }
        } catch (error) {
            console.error('❌ Error recording migration item:', error.message);
        }
    }

    /**
     * Save migration checkpoint
     * @param {string} migrationId - Migration ID
     * @param {number} processedCount - Processed files count
     * @param {Array} results - Current results
     */
    async saveCheckpoint(migrationId, processedCount, results) {
        try {
            console.log(`💾 Saving checkpoint: ${migrationId} - ${processedCount} files processed`);

            const checkpointData = {
                processed_count: processedCount,
                timestamp: new Date().toISOString(),
                results_summary: {
                    total: results.length,
                    successful: results.filter(r => r.success).length,
                    failed: results.filter(r => !r.success).length
                }
            };

            await this.updateMigrationProgress(migrationId, {
                checkpoint_data: checkpointData
            });

        } catch (error) {
            console.error('❌ Error saving checkpoint:', error.message);
        }
    }

    /**
     * Finalize migration
     * @param {string} migrationId - Migration ID
     * @param {Array} results - Migration results
     * @returns {Promise<object>} Final result
     */
    async finalizeMigration(migrationId, results) {
        try {
            const successCount = results.filter(r => r.success).length;
            const failureCount = results.filter(r => !r.success).length;

            const finalData = {
                status: failureCount === 0 ? 'completed' : 'completed_with_errors',
                successful_files: successCount,
                failed_files: failureCount,
                completed_at: new Date().toISOString()
            };

            await this.updateMigrationProgress(migrationId, finalData);

            return {
                migrationId,
                success: true,
                totalFiles: results.length,
                successfulFiles: successCount,
                failedFiles: failureCount,
                results
            };
        } catch (error) {
            console.error('❌ Error finalizing migration:', error.message);
            throw error;
        }
    }

    /**
     * Handle migration error
     * @param {string} migrationId - Migration ID
     * @param {Error} error - Error object
     */
    async handleMigrationError(migrationId, error) {
        try {
            await this.updateMigrationProgress(migrationId, {
                status: 'failed',
                error_message: error.message,
                completed_at: new Date().toISOString()
            });
        } catch (updateError) {
            console.error('❌ Error updating migration error status:', updateError.message);
        }
    }

    /**
     * Start permissions synchronization process for matched files
     * @param {string} syncId - Sync process ID
     * @param {string} userEmail - User email
     * @param {Array} matchedFiles - Array of matched files with larkFileToken and googlePermissions
     * @param {function} progressCallback - Progress callback function
     * @returns {Promise<object>} Sync result
     */
    async startSyncPermissions(syncId, userEmail, matchedFiles, progressCallback = null) {
        try {
            console.log(`🔄 Starting permissions sync: ${syncId} for ${matchedFiles.length} files`);

            // Initialize sync tracking
            const syncProcess = {
                id: syncId,
                userEmail,
                totalFiles: matchedFiles.length,
                processedFiles: 0,
                successfulFiles: 0,
                failedFiles: 0,
                startedAt: new Date().toISOString(),
                errors: []
            };

            this.activeMigrations.set(syncId, syncProcess);

            // Create realtime channel for sync progress
            this.realtime.createMigrationChannel(syncId);

            // Broadcast sync started
            await this.realtime.broadcastStatusChange(syncId, 'syncing_permissions', {
                previousStatus: 'pending',
                reason: 'Permission sync started'
            });

            // Process permissions in batches
            const results = await this.processSyncPermissionsBatch(
                syncId,
                matchedFiles,
                progressCallback
            );

            // Finalize sync process
            const finalResult = await this.finalizeSyncPermissions(syncId, results);

            // Update database to mark files as synced or failed
            await this.updateFilesPermissionStatus(userEmail, matchedFiles, results);

            // Broadcast sync completion
            await this.realtime.broadcastMigrationComplete(syncId, finalResult);

            console.log(`✅ Permissions sync completed: ${syncId}`);
            return finalResult;

        } catch (error) {
            console.error(`❌ Permissions sync failed: ${syncId} - ${error.message}`);

            // Broadcast error
            await this.realtime.broadcastError(syncId, {
                severity: 'critical',
                errorMessage: error.message,
                retryable: false
            });

            throw error;
        } finally {
            this.activeMigrations.delete(syncId);
        }
    }

    /**
     * Process permissions sync sequentially (not in batches)
     * @param {string} syncId - Sync process ID
     * @param {Array} matchedFiles - Matched files array
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<Array>} Sync results
     */
    async processSyncPermissionsBatch(syncId, matchedFiles, progressCallback) {
        const results = [];
        let processedCount = 0;
        let successCount = 0;
        let failureCount = 0;

        console.log(`🔐 Processing permissions sequentially for ${matchedFiles.length} files...`);

        // Process files one by one to avoid API rate limits and ensure stability
        for (let i = 0; i < matchedFiles.length; i++) {
            const matchedFile = matchedFiles[i];

            try {
                console.log(`🔐 Processing file ${i + 1}/${matchedFiles.length}: ${matchedFile.larkFileToken}`);

                const result = await this.syncFilePermissions(
                    syncId,
                    matchedFile,
                    (fileProgress) => {
                        if (progressCallback) {
                            progressCallback({
                                type: 'file_permission_sync',
                                syncId,
                                larkFileToken: matchedFile.larkFileToken,
                                fileIndex: i + 1,
                                totalFiles: matchedFiles.length,
                                ...fileProgress
                            });
                        }
                    }
                );

                processedCount++;

                if (result.success) {
                    successCount++;
                    results.push(result);

                    // Log detailed success info
                    const permInfo = result.permissionErrors ?
                        `with ${result.failedPermissions} permission errors` :
                        `all ${result.successfulPermissions} permissions set successfully`;

                    const skipInfo = result.skippedPermissions > 0 ? `, ${result.skippedPermissions} skipped` : '';
                    console.log(`✅ Successfully synced permissions for file ${i + 1}/${matchedFiles.length} (${permInfo}${skipInfo})`);
                } else {
                    failureCount++;
                    results.push(result);
                    console.error(`❌ Failed to sync permissions for file ${i + 1}/${matchedFiles.length}: ${result.error}`);
                }

            } catch (error) {
                processedCount++;
                failureCount++;

                const errorResult = {
                    success: false,
                    error: error.message || 'Unknown error',
                    larkFileToken: matchedFile.larkFileToken
                };
                results.push(errorResult);

                console.error(`❌ Exception during permission sync for file ${i + 1}/${matchedFiles.length}: ${error.message}`);
            }

            // Update progress after each file
            const progressData = {
                type: 'sync_progress',
                syncId,
                processedFiles: processedCount,
                totalFiles: matchedFiles.length,
                successfulFiles: successCount,
                failedFiles: failureCount,
                progress: (processedCount / matchedFiles.length) * 100,
                currentFile: i + 1
            };

            if (progressCallback) {
                progressCallback(progressData);
            }

            // Broadcast real-time progress
            await this.realtime.broadcastBatchProgress(syncId, progressData);

            // Add delay between files to respect API rate limits
            if (i < matchedFiles.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay between files
            }
        }

        // Calculate detailed statistics
        const totalPermissions = results.reduce((sum, r) => sum + (r.permissionsCount || 0), 0);
        const successfulPermissions = results.reduce((sum, r) => sum + (r.successfulPermissions || 0), 0);
        const failedPermissions = results.reduce((sum, r) => sum + (r.failedPermissions || 0), 0);
        const skippedPermissions = results.reduce((sum, r) => sum + (r.skippedPermissions || 0), 0);

        console.log(`✅ Completed permissions sync: ${successCount} files successful, ${failureCount} files failed out of ${matchedFiles.length} files`);
        console.log(`📊 Permission details: ${successfulPermissions} permissions set successfully, ${skippedPermissions} permissions skipped, ${failedPermissions} permissions failed out of ${totalPermissions} total permissions`);

        return results;
    }

    /**
     * Sync permissions for a single file
     * @param {string} syncId - Sync process ID
     * @param {object} matchedFile - Matched file object with larkFileToken and googlePermissions
     * @param {function} progressCallback - Progress callback
     * @returns {Promise<object>} Sync result
     */
    async syncFilePermissions(syncId, matchedFile, progressCallback) {
        try {
            const { larkFileToken, googlePermissions } = matchedFile;

            if (!googlePermissions || !Array.isArray(googlePermissions)) {
                throw new Error('No Google permissions found for file');
            }

            console.log(`🔐 Syncing permissions for Lark file: ${larkFileToken}`);

            // Report sync start
            if (progressCallback) {
                progressCallback({
                    phase: 'syncing',
                    progress: 0,
                    larkFileToken
                });
            }

            // Set permissions using existing method
            const permissionResults = await this.setFilePermissions(larkFileToken, googlePermissions);

            // Analyze permission setting results
            let successfulPermissions = 0;
            let failedPermissions = 0;
            let skippedPermissions = 0;
            const permissionErrors = [];
            const skippedMessages = [];

            if (permissionResults && Array.isArray(permissionResults)) {
                for (let index = 0; index < permissionResults.length; index++) {
                    const result = permissionResults[index];
                    const googlePermission = googlePermissions[index];

                    if (result.status === 'fulfilled' && result.value) {
                        // Check if this is a skipped permission
                        if (result.value.skip) {
                            skippedPermissions++;
                            skippedMessages.push({
                                message: result.value.message,
                                googlePermission: {
                                    role: googlePermission.role,
                                    type: googlePermission.type,
                                    emailAddress: googlePermission.emailAddress
                                }
                            });
                            console.log(`⏩ Permission skipped: ${result.value.message}`);
                        } else {
                            successfulPermissions++;
                        }
                    } else {
                        failedPermissions++;
                        const error = result.status === 'rejected' ? result.reason : result.value;
                        permissionErrors.push({
                            message: error?.message || 'Unknown permission error',
                            googlePermission: {
                                role: googlePermission.role,
                                type: googlePermission.type,
                                emailAddress: googlePermission.emailAddress
                            }
                        });
                    }
                }
            }

            // Report sync completion
            if (progressCallback) {
                progressCallback({
                    phase: 'completed',
                    progress: 100,
                    larkFileToken,
                    permissionResults: {
                        successful: successfulPermissions,
                        failed: failedPermissions,
                        skipped: skippedPermissions,
                        errors: permissionErrors,
                        skippedMessages: skippedMessages
                    }
                });
            }

            return {
                success: failedPermissions === 0,
                larkFileToken,
                permissionsCount: googlePermissions.length,
                successfulPermissions,
                failedPermissions,
                skippedPermissions,
                permissionErrors: permissionErrors.length > 0 ? permissionErrors : null,
                skippedMessages: skippedMessages.length > 0 ? skippedMessages : null
            };

        } catch (error) {
            console.error(`❌ Permission sync failed for ${matchedFile.larkFileToken}: ${error.message}`);

            // Broadcast file error
            await this.realtime.broadcastError(syncId, {
                severity: 'error',
                larkFileToken: matchedFile.larkFileToken,
                errorMessage: error.message,
                retryable: true
            });

            return {
                success: false,
                larkFileToken: matchedFile.larkFileToken,
                error: error.message
            };
        }
    }

    /**
     * Finalize permissions sync process
     * @param {string} syncId - Sync process ID
     * @param {Array} results - Sync results
     * @returns {Promise<object>} Final result
     */
    async finalizeSyncPermissions(syncId, results) {
        try {
            const successCount = results.filter(r => r.success).length;
            const failureCount = results.filter(r => !r.success).length;

            return {
                syncId,
                success: true,
                totalFiles: results.length,
                successfulFiles: successCount,
                failedFiles: failureCount,
                results
            };
        } catch (error) {
            console.error('❌ Error finalizing permissions sync:', error.message);
            throw error;
        }
    }

    /**
     * Update files permission sync status in database
     * @param {string} userEmail - User email
     * @param {Array} matchedFiles - All matched files
     * @param {Array} results - Sync results corresponding to matched files
     */
    async updateFilesPermissionStatus(userEmail, matchedFiles, results) {
        try {
            if (matchedFiles.length === 0 || results.length === 0) {
                return;
            }

            console.log(`🔄 Updating permission status for ${matchedFiles.length} files...`);

            // Process successful files
            const successfulFiles = [];
            const failedFiles = [];

            for (let i = 0; i < matchedFiles.length; i++) {
                const matchedFile = matchedFiles[i];
                const result = results[i];

                if (result && result.success) {
                    successfulFiles.push({
                        file: matchedFile,
                        result: result
                    });
                } else if (result && !result.success) {
                    failedFiles.push({
                        file: matchedFile,
                        result: result
                    });
                }
            }

            // Update successful files
            if (successfulFiles.length > 0) {
                for (const { file, result } of successfulFiles) {
                    try {
                        const { error: successError } = await supabaseClient.getServiceClient()
                            .from('scanned_lark_files')
                            .update({
                                synced: true,
                                successful_permissions: result.successfulPermissions || 0,
                                failed_permissions: result.failedPermissions || 0,
                                skipped_permissions: result.skippedPermissions || 0,
                                error_messages: result.permissionErrors || null,
                                skipped_messages: result.skippedMessages || null,
                                updated_at: new Date().toISOString()
                            })
                            .eq('user_email', userEmail)
                            .eq('token', file.larkFileToken);

                        if (successError) {
                            console.error(`❌ Error updating successful file ${file.larkFileToken}:`, successError.message);
                        }
                    } catch (fileError) {
                        console.error(`❌ Error processing successful file ${file.larkFileToken}:`, fileError.message);
                    }
                }

                console.log(`✅ Marked ${successfulFiles.length} files as successfully synced`);
            }

            // Update failed files with error details
            if (failedFiles.length > 0) {
                for (const { file, result } of failedFiles) {
                    try {
                        const { error: failError } = await supabaseClient.getServiceClient()
                            .from('scanned_lark_files')
                            .update({
                                synced: false,
                                successful_permissions: result.successfulPermissions || 0,
                                failed_permissions: result.failedPermissions || 0,
                                skipped_permissions: result.skippedPermissions || 0,
                                error_messages: result.permissionErrors || null,
                                skipped_messages: result.skippedMessages || null,
                                updated_at: new Date().toISOString()
                            })
                            .eq('user_email', userEmail)
                            .eq('token', file.larkFileToken);

                        if (failError) {
                            console.error(`❌ Error updating failed file ${file.larkFileToken}:`, failError.message);
                        }
                    } catch (fileError) {
                        console.error(`❌ Error processing failed file ${file.larkFileToken}:`, fileError.message);
                    }
                }

                console.log(`⚠️ Marked ${failedFiles.length} files as failed with error details`);
            }

            console.log(`📊 Permission status update completed: ${successfulFiles.length} successful, ${failedFiles.length} failed`);

        } catch (error) {
            console.error('❌ Error updating files permission status:', error.message);
        }
    }

    /**
     * Mark files as synced in database (legacy method - kept for backward compatibility)
     * @param {string} userEmail - User email
     * @param {Array} successfulFiles - Successfully synced files
     */
    async markFilesAsSynced(userEmail, successfulFiles) {
        try {
            if (successfulFiles.length === 0) {
                return;
            }

            const larkFileTokens = successfulFiles.map(file => file.larkFileToken);

            const { error } = await supabaseClient.getServiceClient()
                .from('scanned_lark_files')
                .update({
                    synced: true,  // Boolean value for successful sync
                })
                .eq('user_email', userEmail)
                .in('token', larkFileTokens);

            if (error) {
                console.error('❌ Error marking files as synced:', error.message);
            } else {
                console.log(`✅ Marked ${successfulFiles.length} files as synced`);
            }
        } catch (error) {
            console.error('❌ Error marking files as synced:', error.message);
        }
    }

    /**
     * Get migration statistics
     * @returns {object} Current statistics
     */
    getStats() {
        return this.stats;
    }

    /**
     * Get active migrations
     * @returns {Array} Active migration information
     */
    getActiveMigrations() {
        return Array.from(this.activeMigrations.entries()).map(([id, migration]) => ({
            migrationId: id,
            ...migration
        }));
    }
}

// Export singleton instance
export const migrationEngine = new MigrationEngine();
