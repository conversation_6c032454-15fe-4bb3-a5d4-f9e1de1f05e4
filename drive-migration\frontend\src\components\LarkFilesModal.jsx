import React, { useState, useEffect } from "react";
import ErrorDisplay from "./ErrorDisplay";
import { apiGet, apiPost, formatError } from "../utils/apiUtils";
import "./LarkFilesModal.css";

const LarkFilesModal = ({
  user,
  isOpen,
  onClose,
  onShowToast
}) => {
  const [larkFiles, setLarkFiles] = useState([]);
  const [larkFilesData, setLarkFilesData] = useState(null);
  const [larkFilesLoading, setLarkFilesLoading] = useState(false);
  const [larkFilesError, setLarkFilesError] = useState(null);
  const [expandedFolders, setExpandedFolders] = useState(new Set());
  const [selectedFiles, setSelectedFiles] = useState(new Set());
  const [syncLoading, setSyncLoading] = useState(false);
  const [rescanLoading, setRescanLoading] = useState(false);

  // Reset state when modal opens/closes or user changes
  useEffect(() => {
    if (isOpen && user) {
      setLarkFiles([]);
      setLarkFilesError(null);
      setLarkFilesLoading(false);
      setExpandedFolders(new Set());
      setSelectedFiles(new Set());
      setSyncLoading(false);
      setRescanLoading(false);
      
      // Auto fetch files when modal opens
      setTimeout(() => {
        fetchLarkFiles(false);
      }, 100);
    }
  }, [isOpen, user]);

  const fetchLarkFiles = async (refresh = false) => {
    if (!user) return;
    
    setLarkFilesLoading(true);
    setLarkFilesError(null);

    try {
      console.log(`📁 Fetching Lark files for user: ${user.email}, refresh: ${refresh}`);
      const result = await apiGet(`/api/lark/users/${encodeURIComponent(user.email)}/lark-files?refresh=${refresh}&includeTree=true`);
      
      setLarkFiles(result.tree || []);
      setLarkFilesData(result);
      // Default collapse all folders
      setExpandedFolders(new Set());
      onShowToast?.("success", `${refresh ? 'Refresh' : 'Tải'} thành công ${result.total || 0} files Lark cho user ${user.fullName}`);
    } catch (error) {
      console.error("Error fetching Lark files:", error);
      const errorInfo = formatError(error);
      setLarkFilesError(error);
      onShowToast?.("error", `Lỗi ${refresh ? 'refresh' : 'tải'} files Lark: ${errorInfo.message}`, {
        showDetails: true,
        details: errorInfo.details,
        duration: 8000,
      });
    } finally {
      setLarkFilesLoading(false);
    }
  };

  const handleRescanFailedFolders = async () => {
    if (!user) return;
    
    setRescanLoading(true);
    try {
      console.log(`🔄 Rescanning failed folders for user: ${user.email}`);
      
      // Call rescan failed folders API
      const result = await apiPost(`/api/lark/users/${encodeURIComponent(user.email)}/rescan-failed-folders`);
      
      if (result.success) {
        onShowToast?.("success", `Đã scan lại ${result.rescannedCount} folders: ${result.successCount} thành công, ${result.failedCount} thất bại`);
        
        // Refresh file list after rescan
        await fetchLarkFiles(false);
      } else {
        throw new Error(result.message || 'Rescan failed');
      }
    } catch (error) {
      const errorInfo = formatError(error);
      onShowToast?.("error", `Lỗi scan lại folder: ${errorInfo.message}`, {
        showDetails: true,
        details: errorInfo.details,
        duration: 8000,
      });
    } finally {
      setRescanLoading(false);
    }
  };

  const toggleFolder = (folderId) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  const findItemByToken = (items, token) => {
    for (const item of items) {
      if (item.id === token) {
        return item;
      }
      if (item.children) {
        const found = findItemByToken(item.children, token);
        if (found) return found;
      }
    }
    return null;
  };

  const toggleFileSelection = (fileToken, isFolder = false) => {
    setSelectedFiles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(fileToken)) {
        newSet.delete(fileToken);
        // If folder, unselect all children
        if (isFolder) {
          const removeChildren = (items) => {
            items.forEach(item => {
              newSet.delete(item.id);
              if (item.children) {
                removeChildren(item.children);
              }
            });
          };
          const folderItem = findItemByToken(larkFiles, fileToken);
          if (folderItem && folderItem.children) {
            removeChildren(folderItem.children);
          }
        }
      } else {
        newSet.add(fileToken);
        // If folder, select all children
        if (isFolder) {
          const addChildren = (items) => {
            items.forEach(item => {
              newSet.add(item.id);
              if (item.children) {
                addChildren(item.children);
              }
            });
          };
          const folderItem = findItemByToken(larkFiles, fileToken);
          if (folderItem && folderItem.children) {
            addChildren(folderItem.children);
          }
        }
      }
      return newSet;
    });
  };

  const handleSelectAllFiles = () => {
    const allTokens = [];
    const collectTokens = (items) => {
      items.forEach(item => {
        allTokens.push(item.id);
        if (item.children) {
          collectTokens(item.children);
        }
      });
    };
    collectTokens(larkFiles);
    
    if (selectedFiles.size === allTokens.length) {
      setSelectedFiles(new Set());
    } else {
      setSelectedFiles(new Set(allTokens));
    }
  };

  const handleSyncPermissions = async () => {
    if (selectedFiles.size === 0) {
      onShowToast?.("warning", "Vui lòng chọn ít nhất một file để đồng bộ permissions");
      return;
    }

    setSyncLoading(true);
    try {
      const fileTokens = Array.from(selectedFiles);
      console.log(`Syncing permissions for ${fileTokens.length} files for user: ${user.email}`);
      
      // Prepare API payload
      const payload = {
        email: user.email
      };
      
      // Only include larkFileIds if not selecting all files
      const totalFiles = larkFiles.reduce((count, item) => {
        const countFiles = (items) => {
          let total = 0;
          items.forEach(item => {
            total++;
            if (item.children) total += countFiles(item.children);
          });
          return total;
        };
        return count + countFiles([item]);
      }, 0);
      
      // If not all files are selected, include specific file IDs
      if (selectedFiles.size !== totalFiles) {
        payload.larkFileIds = fileTokens;
      }
      
      // Call sync permissions API
      const result = await apiPost("/api/migration/sync-permissions", payload);
      
      console.log("Sync permissions result:", result);
      onShowToast?.("success", `Đồng bộ permissions thành công cho ${fileTokens.length} files!`);
      
      // Reset selected files after successful sync
      setSelectedFiles(new Set());
      
    } catch (error) {
      console.error("Error syncing permissions:", error);
      const errorInfo = formatError(error);
      onShowToast?.("error", `Lỗi đồng bộ permissions: ${errorInfo.message}`, {
        showDetails: true,
        details: errorInfo.details,
        duration: 8000,
      });
    } finally {
      setSyncLoading(false);
    }
  };

  // Helper function to check if a folder or its descendants have unscanned folders
  const hasUnscannedDescendants = (item) => {
    if (item.type === "folder" && item.scanned === false) {
      return true;
    }
    if (item.children) {
      return item.children.some(child => hasUnscannedDescendants(child));
    }
    return false;
  };

  const renderLarkFileTree = (items, level = 0) => {
    return items.map((item) => {
      const itemId = item.id || `${item.name}-${level}`;
      const isFolder = item.type === "folder";
      const isExpanded = expandedFolders.has(itemId);
      const isSelected = selectedFiles.has(itemId);
      
      // Check if this folder or any of its descendants are unscanned
      const hasUnscannedFolders = isFolder && hasUnscannedDescendants(item);
      
      return (
        <div key={itemId} style={{ marginLeft: `${level * 20}px` }}>
          <div
            style={{
              padding: "0.5rem",
              borderRadius: "4px",
              background: level % 2 === 0 ? "#f9fafb" : "#ffffff",
              border: "1px solid #e5e7eb",
              marginBottom: "2px",
              display: "flex",
              alignItems: "center",
              gap: "0.5rem",
              transition: "background-color 0.2s",
            }}
            onMouseEnter={(e) => {
              if (isFolder) {
                e.target.style.backgroundColor = level % 2 === 0 ? "#f3f4f6" : "#f9fafb";
              }
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = level % 2 === 0 ? "#f9fafb" : "#ffffff";
            }}
          >
            {/* Checkbox */}
            <label style={{ cursor: "pointer", display: "flex", alignItems: "center" }}>
              <input
                type="checkbox"
                checked={isSelected}
                onChange={(e) => {
                  e.stopPropagation();
                  toggleFileSelection(itemId, isFolder);
                }}
                style={{
                  width: "14px",
                  height: "14px",
                  cursor: "pointer",
                  accentColor: "#3b82f6"
                }}
              />
            </label>
            
            {/* Folder expand/collapse */}
            {isFolder && (
              <span 
                style={{ 
                  fontSize: "0.75rem", 
                  color: "#6b7280",
                  minWidth: "16px",
                  textAlign: "center",
                  userSelect: "none",
                  cursor: "pointer"
                }}
                onClick={() => toggleFolder(itemId)}
              >
                {isExpanded ? "▼" : "▶"}
              </span>
            )}
            
            {/* File/Folder icon */}
            <span style={{ fontSize: "1rem" }}>
              {isFolder ? (hasUnscannedFolders ? "📂" : "📁") : "📄"}
            </span>
            
            {/* File/Folder name */}
            <span 
              style={{ 
                fontSize: "0.875rem", 
                flex: 1,
                cursor: isFolder ? "pointer" : "default",
                userSelect: "none",
                color: hasUnscannedFolders ? "#dc2626" : "inherit"
              }}
              onClick={() => isFolder && toggleFolder(itemId)}
            >
              {item.name}
              {isFolder && item.scanned === false && (
                <span style={{ 
                  marginLeft: "0.5rem", 
                  fontSize: "0.75rem",
                  color: "#dc2626",
                  fontWeight: "500"
                }}>
                  (chưa scan)
                </span>
              )}
            </span>
            
            {/* Children count for folders */}
            {isFolder && item.children && (
              <span style={{ 
                fontSize: "0.75rem", 
                color: "#6b7280",
                background: "#e5e7eb",
                borderRadius: "10px",
                padding: "2px 6px",
                minWidth: "20px",
                textAlign: "center"
              }}>
                {item.children.length}
              </span>
            )}
          </div>
          {isFolder && item.children && item.children.length > 0 && isExpanded && 
            renderLarkFileTree(item.children, level + 1)
          }
        </div>
      );
    });
  };

  if (!isOpen || !user) {
    return null;
  }

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
      onClick={onClose}
    >
      <div
        style={{
          background: "white",
          borderRadius: "8px",
          padding: "1.5rem",
          maxWidth: "90vw",
          maxHeight: "90vh",
          overflow: "auto",
          boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",
          width: "800px",
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "1rem",
            borderBottom: "1px solid #e5e7eb",
            paddingBottom: "1rem",
          }}
        >
          <div>
            <h3 style={{ margin: 0, fontSize: "1.25rem", fontWeight: "600" }}>
              📁 Files Lark - {user.fullName}
            </h3>
            <p style={{ margin: "0.25rem 0 0 0", color: "#6b7280", fontSize: "0.875rem" }}>
              Folder: {user.larkUserFolder}
            </p>
          </div>
          <button
            onClick={onClose}
            style={{
              background: "transparent",
              border: "none",
              fontSize: "1.5rem",
              cursor: "pointer",
              padding: "0.5rem",
              borderRadius: "4px",
              color: "#6b7280",
            }}
            onMouseEnter={(e) => {
              e.target.style.background = "#f3f4f6";
            }}
            onMouseLeave={(e) => {
              e.target.style.background = "transparent";
            }}
          >
            ✕
          </button>
        </div>

        {/* Action buttons */}
        <div style={{ marginBottom: "1rem", display: "flex", gap: "0.5rem", justifyContent: "flex-end" }}>
          <button
            onClick={handleSyncPermissions}
            disabled={larkFilesLoading || syncLoading || selectedFiles.size === 0}
            style={{
              padding: "0.5rem 1rem",
              borderRadius: "6px",
              border: "1px solid #16a34a",
              background: selectedFiles.size === 0 ? "#9ca3af" : "#16a34a",
              color: "white",
              fontSize: "0.875rem",
              cursor: (larkFilesLoading || syncLoading || selectedFiles.size === 0) ? "not-allowed" : "pointer",
              opacity: (larkFilesLoading || syncLoading) ? 0.6 : 1,
              display: "flex",
              alignItems: "center",
              gap: "0.5rem",
            }}
          >
            {syncLoading ? (
              <>
                <span
                  className="spinner"
                  style={{ width: "14px", height: "14px" }}
                ></span>
                Đang đồng bộ...
              </>
            ) : (
              <>
                🔗 Đồng bộ permissions ({selectedFiles.size} files)
              </>
            )}
          </button>
          {larkFilesData?.failedFolderCount > 0 && (
            <button
              onClick={handleRescanFailedFolders}
              disabled={larkFilesLoading || syncLoading || rescanLoading}
              style={{
                padding: "0.5rem 1rem",
                borderRadius: "6px",
                border: "1px solid #f59e0b",
                background: "#f59e0b",
                color: "white",
                fontSize: "0.875rem",
                cursor: (larkFilesLoading || syncLoading || rescanLoading) ? "not-allowed" : "pointer",
                opacity: (larkFilesLoading || syncLoading || rescanLoading) ? 0.6 : 1,
                display: "flex",
                alignItems: "center",
                gap: "0.5rem",
              }}
            >
              {rescanLoading ? (
                <>
                  <span
                    className="spinner"
                    style={{ width: "14px", height: "14px" }}
                  ></span>
                  Đang scan lại...
                </>
              ) : (
                <>
                  🔄 Scan lại folder lỗi ({larkFilesData?.failedFolderCount})
                </>
              )}
            </button>
          )}
          <button
            onClick={() => fetchLarkFiles(true)}
            disabled={larkFilesLoading || syncLoading}
            style={{
              padding: "0.5rem 1rem",
              borderRadius: "6px",
              border: "1px solid #3b82f6",
              background: "#3b82f6",
              color: "white",
              fontSize: "0.875rem",
              cursor: (larkFilesLoading || syncLoading) ? "not-allowed" : "pointer",
              opacity: (larkFilesLoading || syncLoading) ? 0.6 : 1,
              display: "flex",
              alignItems: "center",
              gap: "0.5rem",
            }}
          >
            {larkFilesLoading ? (
              <>
                <span
                  className="spinner"
                  style={{ width: "14px", height: "14px" }}
                ></span>
                Đang refresh...
              </>
            ) : (
              <>
                🔄 Refresh từ Lark
              </>
            )}
          </button>
        </div>

        {larkFilesError && (
          <ErrorDisplay
            error={larkFilesError}
            title="Lỗi tải files Lark"
            onDismiss={() => setLarkFilesError(null)}
            onRetry={() => fetchLarkFiles(false)}
          />
        )}

        {larkFilesLoading ? (
          <div className="loading-state" style={{ textAlign: "center", padding: "2rem" }}>
            <div className="spinner"></div>
            <p>Đang tải files Lark...</p>
          </div>
        ) : larkFiles.length === 0 ? (
          <div style={{ textAlign: "center", padding: "2rem", color: "#6b7280" }}>
            <p>📂 Không có files nào trong folder Lark này</p>
          </div>
        ) : (
          <div>
            <div style={{ 
              marginBottom: "1rem", 
              fontSize: "0.875rem", 
              color: "#6b7280",
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center"
            }}>
              <div style={{ display: "flex", alignItems: "center", gap: "1rem" }}>
                <span style={{ color: "#dc2626" }}>
                  Số lượng folder scan bị lỗi: <strong>{larkFilesData?.failedFolderCount || 0}</strong> folders
                </span>
                <span>
                  Tổng số files: {larkFilesData?.fileCount || 0}
                </span>
                <span>
                  Đã chọn: {selectedFiles.size} files
                </span>
                <span style={{ fontSize: "0.75rem", color: "#6b7280", fontStyle: "italic" }}>
                  📂 Folder màu đỏ: chưa được scan hoặc chứa folder chưa scan
                </span>
                <button
                  onClick={handleSelectAllFiles}
                  style={{
                    fontSize: "0.75rem",
                    padding: "0.25rem 0.5rem",
                    border: "1px solid #3b82f6",
                    borderRadius: "4px",
                    background: "transparent",
                    color: "#3b82f6",
                    cursor: "pointer"
                  }}
                >
                  {selectedFiles.size === 0 ? "✅ Chọn tất cả" : "❌ Bỏ chọn tất cả"}
                </button>
              </div>
            </div>
            <div
              style={{
                maxHeight: "60vh",
                overflow: "auto",
                border: "1px solid #e5e7eb",
                borderRadius: "6px",
                padding: "1rem",
              }}
            >
              {renderLarkFileTree(larkFiles)}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default LarkFilesModal;
