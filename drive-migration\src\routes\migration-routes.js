import express from 'express';
import { migrationEngine } from '../services/migration-engine.js';
import { supabaseClient } from '../database/supabase.js';
import { v4 as uuidv4 } from 'uuid';
import { google } from 'googleapis';

const router = express.Router();

/**
 * Start migration process
 * POST /api/migration/start
 */
router.post('/start', async (req, res) => {
    try {
        const { userEmail, scanSessionId: sessionId, options = {} } = req.body;

        if (!userEmail || !sessionId) {
            return res.status(400).json({
                error: 'userEmail and sessionId are required'
            });
        }

        console.log(`🚀 Starting migration for session: ${sessionId}`);

        // Generate migration ID
        const migrationId = uuidv4();

        // Start migration with progress tracking
        const migrationPromise = migrationEngine.startMigration(
            migrationId,
            userEmail,
            sessionId,
            options,
            (progress) => {
                // Progress is now handled via real-time updates
                console.log(`📊 Migration progress:`, progress.type || 'update');
            }
        );

        // Return immediately with migration ID
        // The actual migration runs in background

        res.json({
            success: true,
            message: 'Migration started successfully',
            migrationId,
            status: 'running'
        });

        // Wait for migration to complete (in background)
        try {
            const result = await migrationPromise;
            console.log(`✅ Migration completed: ${migrationId}`, result);
        } catch (error) {
            console.error(`❌ Migration failed: ${migrationId}`, error.message);
        }

    } catch (error) {
        console.error('❌ Error starting migration:', error.message);
        res.status(500).json({
            error: 'Failed to start migration',
            details: error.message
        });
    }
});

/**
 * Sync Permissions
 * POST /api/migration/sync-permissions
 */
router.post('/sync-permissions', async (req, res) => {
    try {
        const { email, larkFileIds } = req.body;

        if (!email) {
            return res.status(400).json({
                error: 'email is required'
            });
        }

        // Use the main view to get matched files with permissions
        let query = supabaseClient
            .getServiceClient()
            .from("lark_google_files_with_permissions")
            .select("*")
            .eq("user_email", email)
            .eq("type", "file") // Only files can have synced status
            .eq("synced", false);  // Only files not yet synced

        // Add filter for specific larkFileIds if provided
        if (larkFileIds && larkFileIds.length > 0) {
            query = query.in("id", larkFileIds);
        }

        const { data: filesNeedingSync, error: syncError } = await query
            .order("full_path", { ascending: true });

        if (syncError) {
            throw new Error(`Error fetching files needing sync: ${syncError.message}`);
        }

        // Check if no files need sync
        if (!filesNeedingSync || filesNeedingSync.length === 0) {
            return res.json({
                success: true,
                message: 'No files found that need permission sync',
                result: {
                    syncId: null,
                    success: true,
                    totalFiles: 0,
                    successfulFiles: 0,
                    failedFiles: 0,
                    results: []
                },
                totalFilesToSync: 0
            });
        }

        // Transform the data for the migration engine
        const matchedFiles = filesNeedingSync.map(file => ({
            larkFileToken: file.token,
            googlePermissions: file.google_permissions,
        }));

        // Start sync with progress tracking
        const syncId = uuidv4();
        const result = await migrationEngine.startSyncPermissions(
            syncId,
            email,
            matchedFiles,
            (progress) => {
                // Progress is now handled via real-time updates
                console.log(`📊 sync progress:`, progress.type || 'update');
            }
        );

        res.json({
            success: true,
            message: 'sync started successfully',
            result,
            totalFilesToSync: matchedFiles.length
        });

    } catch (error) {
        console.error('❌ Error starting sync:', error.message);
        res.status(500).json({
            error: 'Failed to start sync',
            details: error.message
        });
    }
});

/**
 * Get sync statistics for a user
 * GET /api/migration/sync-stats/:email
 */
router.get('/sync-stats/:email', async (req, res) => {
    try {
        const { email } = req.params;

        if (!email) {
            return res.status(400).json({
                error: 'email is required'
            });
        }

        // Get sync statistics from the view
        const { data: stats, error: statsError } = await supabaseClient
            .getServiceClient()
            .from("lark_google_sync_stats")
            .select("*")
            .eq("user_email", email)
            .single();

        if (statsError) {
            throw new Error(`Error fetching sync stats: ${statsError.message}`);
        }

        // Get detailed breakdown of files
        const { data: fileBreakdown, error: breakdownError } = await supabaseClient
            .getServiceClient()
            .from("lark_google_files_with_permissions")
            .select("match_status")
            .eq("user_email", email);

        if (breakdownError) {
            throw new Error(`Error fetching file breakdown: ${breakdownError.message}`);
        }

        // Calculate additional statistics
        const additionalStats = {
            needsSyncCount: fileBreakdown.length, // All files returned need sync
            totalMatchedFiles: fileBreakdown.filter(f =>
                f.match_status === 'matched'
            ).length,
        };

        res.json({
            success: true,
            email: email,
            stats: stats || {
                total_lark_files: 0,
                matched_files: 0,
                lark_only_files: 0,
                match_percentage: 0
            },
            additionalStats,
            breakdown: fileBreakdown
        });

    } catch (error) {
        console.error('❌ Error getting sync stats:', error.message);
        res.status(500).json({
            error: 'Failed to get sync stats',
            details: error.message
        });
    }
});

/**
 * Get files needing permission sync for a user
 * GET /api/migration/files-needing-sync/:email
 */
router.get('/files-needing-sync/:email', async (req, res) => {
    try {
        const { email } = req.params;
        const { page = 1, pageSize = 50 } = req.query;

        if (!email) {
            return res.status(400).json({
                error: 'email is required'
            });
        }

        // Get files that need permission sync with pagination
        const offset = (parseInt(page) - 1) * parseInt(pageSize);

        const { data: files, error: filesError, count } = await supabaseClient
            .getServiceClient()
            .from("matched_lark_google_files")
            .select("id, token, name, full_path, user_email, synced", { count: "exact" })
            .eq("user_email", email)
            .eq("type", "file") // Only files can have synced status
            .eq("synced", false)  // Only files not yet synced (BOOLEAN type)
            .order("full_path", { ascending: true })
            .range(offset, offset + parseInt(pageSize) - 1);

        if (filesError) {
            throw new Error(`Error fetching files needing sync: ${filesError.message}`);
        }

        res.json({
            success: true,
            files: files || [],
            totalCount: count || 0,
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            totalPages: Math.ceil((count || 0) / parseInt(pageSize))
        });

    } catch (error) {
        console.error('❌ Error getting files needing sync:', error.message);
        res.status(500).json({
            error: 'Failed to get files needing sync',
            details: error.message
        });
    }
});



export default router;
