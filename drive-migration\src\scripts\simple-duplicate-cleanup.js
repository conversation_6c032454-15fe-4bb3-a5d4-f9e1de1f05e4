#!/usr/bin/env node

import { DuplicateFileRemover } from './remove-duplicate-files.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Simple script để chạy duplicate cleanup mà không cần SQL functions phức tạp
 */
async function simpleDuplicateCleanup() {
    console.log('🚀 Starting simple duplicate file cleanup...');
    console.log('='.repeat(60));
    console.log('');

    try {
        const remover = new DuplicateFileRemover();
        await remover.run();
        
        console.log('='.repeat(60));
        console.log('🎉 COMPLETE: Duplicate cleanup finished successfully!');
        
    } catch (error) {
        console.error('💥 FAILED: Duplicate cleanup failed');
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
    simpleDuplicateCleanup();
}

export { simpleDuplicateCleanup };
