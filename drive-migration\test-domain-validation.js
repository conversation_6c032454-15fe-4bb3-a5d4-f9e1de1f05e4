/**
 * Test domain validation functionality
 */

import { MigrationEngine } from '../src/services/migration-engine.js';

// Test the domain validation method
const testDomainValidation = () => {
    console.log('🧪 Testing domain validation...');

    // Mock environment variable for testing
    process.env.GOOGLE_DOMAIN = 'osp.vn';

    const migrationEngine = new MigrationEngine();

    const testCases = [
        {
            email: '<EMAIL>',
            expected: true,
            description: 'Valid email with correct domain'
        },
        {
            email: '<EMAIL>',
            expected: true,
            description: 'Valid email with correct domain (admin)'
        },
        {
            email: '<EMAIL>',
            expected: false,
            description: 'Valid email with incorrect domain'
        },
        {
            email: '<EMAIL>',
            expected: false,
            description: 'Valid email with similar but incorrect domain'
        },
        {
            email: 'invalid-email',
            expected: false,
            description: 'Invalid email format'
        },
        {
            email: '',
            expected: false,
            description: 'Empty email'
        },
        {
            email: null,
            expected: false,
            description: 'Null email'
        }
    ];

    let passedTests = 0;
    let totalTests = testCases.length;

    testCases.forEach((testCase, index) => {
        const result = migrationEngine.validateEmailDomain(testCase.email);
        const passed = result.isValid === testCase.expected;

        console.log(`${passed ? '✅' : '❌'} Test ${index + 1}: ${testCase.description}`);
        console.log(`   Email: ${testCase.email}`);
        console.log(`   Expected: ${testCase.expected}, Got: ${result.isValid}`);
        console.log(`   Reason: ${result.reason}`);

        if (passed) {
            passedTests++;
        }

        console.log('');
    });

    console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`);

    if (passedTests === totalTests) {
        console.log('🎉 All domain validation tests passed!');
        return true;
    } else {
        console.log('❌ Some domain validation tests failed!');
        return false;
    }
};

// Test permission setting with domain validation
const testPermissionWithDomainValidation = () => {
    console.log('🧪 Testing permission setting with domain validation...');

    process.env.GOOGLE_DOMAIN = 'osp.vn';

    const migrationEngine = new MigrationEngine();

    const testPermissions = [
        {
            role: 'writer',
            type: 'user',
            emailAddress: '<EMAIL>'
        },
        {
            role: 'reader',
            type: 'user',
            emailAddress: '<EMAIL>' // This should fail domain validation
        },
        {
            role: 'reader',
            type: 'anyone',
            emailAddress: null // This should work as it's 'anyone'
        }
    ];

    console.log('Test permissions:', JSON.stringify(testPermissions, null, 2));

    // This is just to show the validation logic in action
    // In real usage, setFilePermissions would be called
    testPermissions.forEach((permission, index) => {
        if (permission.emailAddress) {
            const validation = migrationEngine.validateEmailDomain(permission.emailAddress);
            console.log(`Permission ${index + 1}: ${permission.emailAddress} - ${validation.isValid ? 'VALID' : 'INVALID'} (${validation.reason})`);
        } else {
            console.log(`Permission ${index + 1}: No email address (${permission.type})`);
        }
    });
};

// Run tests
console.log('🚀 Starting domain validation tests...\n');

testDomainValidation();
console.log('\n' + '='.repeat(50) + '\n');
testPermissionWithDomainValidation();
