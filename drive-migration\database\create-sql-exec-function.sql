-- Create SQL execution function for administrative tasks
-- This function allows executing arbitrary SQL statements from the application

CREATE OR REPLACE FUNCTION exec_sql(sql TEXT)
RETURNS TEXT AS $$
DECLARE
    result TEXT;
BEGIN
    -- Execute the SQL statement
    EXECUTE sql;
    
    -- Return success message
    RETURN 'SQL executed successfully';
EXCEPTION
    WHEN OTHERS THEN
        -- Return error message
        RETURN 'Error: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to service role
-- Note: This should only be used with service role key, not anon key
GRANT EXECUTE ON FUNCTION exec_sql(TEXT) TO service_role;

-- Create helper function to get duplicate file IDs
CREATE OR REPLACE FUNCTION get_duplicate_file_ids()
RETURNS TABLE(file_id TEXT, duplicate_count BIGINT) AS $$
BEGIN
    RETURN QUERY
    SELECT sf.file_id, COUNT(*)::BIGINT as duplicate_count
    FROM scanned_files sf
    GROUP BY sf.file_id
    HAVING COUNT(*) > 1
    ORDER BY COUNT(*) DESC;
END;
$$ LANGUAGE plpgsql;

-- <PERSON> execute permission
GRANT EXECUTE ON FUNCTION get_duplicate_file_ids() TO service_role;
GRANT EXECUTE ON FUNCTION get_duplicate_file_ids() TO anon;
