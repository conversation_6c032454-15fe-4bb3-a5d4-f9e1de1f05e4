import { migrationEngine } from './src/services/migration-engine.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Test script to verify skipped messages functionality
 */
async function testSkippedMessages() {
    console.log('🧪 Testing skipped messages functionality...');

    // Test validateEmailDomain method
    console.log('\n1. Testing domain validation...');

    const validEmail = '<EMAIL>';
    const invalidEmail = '<EMAIL>';
    const invalidFormatEmail = 'invalid-email';

    const validResult = migrationEngine.validateEmailDomain(validEmail);
    const invalidResult = migrationEngine.validateEmailDomain(invalidEmail);
    const invalidFormatResult = migrationEngine.validateEmailDomain(invalidFormatEmail);

    console.log(`✅ Valid email (${validEmail}):`, validResult);
    console.log(`❌ Invalid email (${invalidEmail}):`, invalidResult);
    console.log(`❌ Invalid format (${invalidFormatEmail}):`, invalidFormatResult);

    // Test setFilePermissions with various permission types
    console.log('\n2. Testing setFilePermissions with skipped messages...');

    const testPermissions = [
        {
            role: 'reader',
            type: 'user',
            emailAddress: '<EMAIL>'
        },
        {
            role: 'writer',
            type: 'user',
            emailAddress: '<EMAIL>'
        },
        {
            role: 'reader',
            type: 'domain',
            emailAddress: '<EMAIL>'
        },
        {
            role: 'reader',
            type: 'anyone',
            emailAddress: null
        }
    ];

    try {
        const mockFileToken = 'test-file-token-123';
        const permissionResults = await migrationEngine.setFilePermissions(mockFileToken, testPermissions);

        console.log('\n📊 Permission Results:');
        permissionResults.forEach((result, index) => {
            console.log(`Permission ${index + 1}:`, result);
        });

        // Count results
        const successful = permissionResults.filter(r => r.status === 'fulfilled' && r.value && !r.value.skip).length;
        const skipped = permissionResults.filter(r => r.status === 'fulfilled' && r.value && r.value.skip).length;
        const failed = permissionResults.filter(r => r.status === 'rejected').length;

        console.log(`\n📈 Summary: ${successful} successful, ${skipped} skipped, ${failed} failed`);

    } catch (error) {
        console.error('❌ Error testing setFilePermissions:', error.message);
    }

    // Test syncFilePermissions
    console.log('\n3. Testing syncFilePermissions with skipped messages...');

    const mockMatchedFile = {
        larkFileToken: 'mock-lark-token-456',
        googlePermissions: testPermissions
    };

    try {
        const syncResult = await migrationEngine.syncFilePermissions(
            'test-sync-id',
            mockMatchedFile,
            (progress) => {
                console.log('🔄 Progress:', progress);
            }
        );

        console.log('\n📊 Sync Result:', syncResult);

        if (syncResult.skippedMessages && syncResult.skippedMessages.length > 0) {
            console.log('\n⏩ Skipped Messages:');
            syncResult.skippedMessages.forEach((skip, index) => {
                console.log(`  ${index + 1}. ${skip.message}`);
                console.log(`     Permission: ${skip.googlePermission.type} - ${skip.googlePermission.role} - ${skip.googlePermission.emailAddress}`);
            });
        }

    } catch (error) {
        console.error('❌ Error testing syncFilePermissions:', error.message);
    }

    console.log('\n✅ Skipped messages test completed!');
}

// Run the test
testSkippedMessages().catch(console.error);
