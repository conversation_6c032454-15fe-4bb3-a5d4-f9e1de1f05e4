/**
 * Test domain validation and skip functionality
 */

import { MigrationEngine } from './src/services/migration-engine.js';

// Test the skip functionality
const testSkipFunctionality = async () => {
    console.log('🧪 Testing skip functionality...');

    // Mock environment variable for testing
    process.env.GOOGLE_DOMAIN = 'osp.vn';

    const migrationEngine = new MigrationEngine();

    // Mock file token
    const fileToken = 'test-file-token';

    // Test permissions with mixed scenarios
    const testPermissions = [
        {
            role: 'writer',
            type: 'user',
            emailAddress: '<EMAIL>' // Should succeed
        },
        {
            role: 'reader',
            type: 'user',
            emailAddress: '<EMAIL>' // Should be skipped due to domain validation
        },
        {
            role: 'reader',
            type: 'anyone',
            emailAddress: null // Should succeed
        },
        {
            role: 'reader',
            type: 'domain',
            emailAddress: '<EMAIL>' // Should be skipped as unsupported type
        }
    ];

    console.log('Test permissions:', JSON.stringify(testPermissions, null, 2));

    try {
        // Mock the larkAPI methods to avoid actual API calls
        migrationEngine.larkAPI = {
            addPermissions: async (token, config) => {
                console.log(`✅ Mock addPermissions called for ${config.member_id}`);
                return { success: true, member_id: config.member_id };
            },
            setPermissions: async (token, config) => {
                console.log(`✅ Mock setPermissions called for anyone`);
                return { success: true };
            },
            transferOwnership: async (token, email) => {
                console.log(`✅ Mock transferOwnership called for ${email}`);
                return { success: true, email };
            }
        };

        // Test the permission setting
        const results = await migrationEngine.setFilePermissions(fileToken, testPermissions);

        console.log('\n📊 Results:');
        results.forEach((result, index) => {
            console.log(`Result ${index + 1}:`, {
                status: result.status,
                skip: result.value?.skip,
                message: result.value?.message,
                emailAddress: result.value?.emailAddress,
                type: result.value?.type,
                role: result.value?.role
            });
        });

        // Count results
        const successful = results.filter(r => r.status === 'fulfilled' && r.value && !r.value.skip).length;
        const skipped = results.filter(r => r.status === 'fulfilled' && r.value && r.value.skip).length;
        const failed = results.filter(r => r.status === 'rejected').length;

        console.log(`\n📈 Summary: ${successful} successful, ${skipped} skipped, ${failed} failed`);

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    }
};

// Test domain validation function directly
const testDomainValidation = () => {
    console.log('\n🧪 Testing domain validation function...');

    process.env.GOOGLE_DOMAIN = 'osp.vn';
    const migrationEngine = new MigrationEngine();

    const testEmails = [
        '<EMAIL>',      // Should pass
        '<EMAIL>',   // Should fail
        '<EMAIL>',     // Should pass
        '<EMAIL>',  // Should fail
        'invalid-email',    // Should fail
        null,               // Should fail
        ''                  // Should fail
    ];

    testEmails.forEach(email => {
        const result = migrationEngine.validateEmailDomain(email);
        console.log(`${result.isValid ? '✅' : '❌'} ${email || 'null/empty'}: ${result.reason}`);
    });
};

// Run tests
console.log('🚀 Starting skip functionality tests...\n');

testDomainValidation();
console.log('\n' + '='.repeat(60) + '\n');
await testSkipFunctionality();
