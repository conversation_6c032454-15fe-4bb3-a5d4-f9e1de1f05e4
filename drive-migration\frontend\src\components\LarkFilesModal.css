/* LarkFilesModal.css */

.lark-files-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.lark-files-modal-content {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  width: 800px;
}

.lark-files-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 1rem;
}

.lark-files-modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.lark-files-modal-subtitle {
  margin: 0.25rem 0 0 0;
  color: #6b7280;
  font-size: 0.875rem;
}

.lark-files-modal-close {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 4px;
  color: #6b7280;
  transition: background-color 0.2s;
}

.lark-files-modal-close:hover {
  background: #f3f4f6;
}

.lark-files-modal-actions {
  margin-bottom: 1rem;
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.lark-files-modal-action-btn {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: 1px solid;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: opacity 0.2s;
}

.lark-files-modal-action-btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.lark-files-modal-sync-btn {
  border-color: #16a34a;
  background: #16a34a;
  color: white;
}

.lark-files-modal-sync-btn:disabled {
  background: #9ca3af;
}

.lark-files-modal-rescan-btn {
  border-color: #f59e0b;
  background: #f59e0b;
  color: white;
}

.lark-files-modal-refresh-btn {
  border-color: #3b82f6;
  background: #3b82f6;
  color: white;
}

.lark-files-modal-stats {
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.lark-files-modal-stats-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.lark-files-modal-stats-right {
  display: flex;
  gap: 0.5rem;
}

.lark-files-modal-expand-btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: #f9fafb;
  color: #374151;
  cursor: pointer;
}

.lark-files-modal-tree-container {
  max-height: 60vh;
  overflow: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 1rem;
}

.lark-files-modal-loading {
  text-align: center;
  padding: 2rem;
}

.lark-files-modal-empty {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
}

/* Spinner animation */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  animation: spin 1s linear infinite;
  display: inline-block;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
