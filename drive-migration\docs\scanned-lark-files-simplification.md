# Scanned Lark Files Table Simplification

## Overview

Updated the `scanned_lark_files` table schema to use a simplified structure focused on basic tracking rather than detailed permission statistics.

## Changes Made

### 1. Database Schema Changes

#### Table Structure Updates:

- **Changed `id` column**: From `UUID` to `SERIAL PRIMARY KEY`
- **Removed detailed permission columns**:
  - `owners` (JSONB)
  - `permissions` (JSONB)
  - `synced_permissions` (BOOLEAN)
  - `permission_errors` (JSONB)
  - `successful_permissions` (INTEGER)
  - `failed_permissions` (INTEGER)
  - `skipped_permissions` (INTEGER)
  - `skipped_messages` (JSONB)
  - `size` (BIGINT)
  - `created_time` (TIMESTAMPTZ)
  - `modified_time` (TIMESTAMPTZ)

#### New Simplified Columns:

- **`synced`** (BOOLEAN): Tracks if permissions have been synchronized (only used for files)
- **`scanned`** (BOOLEAN): Tracks if the folder has been scanned (only used for folders)

#### Final Table Structure:

```sql
CREATE TABLE scanned_lark_files (
  id SERIAL PRIMARY KEY,
  token TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  full_path TEXT NOT NULL,
  type TEXT NOT NULL,
  parent_token TEXT,
  url TEXT,
  user_email TEXT NOT NULL,
  synced BOOLEAN DEFAULT false, -- Only for files
  scanned BOOLEAN DEFAULT false, -- Only for folders
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  -- Constraints to ensure proper field usage
  CONSTRAINT check_synced_only_for_files CHECK (
    (type = 'file' AND synced IS NOT NULL) OR
    (type != 'file' AND synced IS NULL)
  ),
  CONSTRAINT check_scanned_only_for_folders CHECK (
    (type = 'folder' AND scanned IS NOT NULL) OR
    (type != 'folder' AND scanned IS NULL)
  )
);
```

### 2. Related Table Updates

#### `sync_lark_permissions` Table:

- Updated foreign key reference from `UUID` to `INTEGER`
- Changed `lark_file_id` column type to match new SERIAL id

### 3. Code Updates

#### Files Modified:

1. **`database/schema.sql`**: Updated table definition and indexes
2. **`src/routes/scan-routes.js`**:
   - Removed `uuidv4()` usage
   - Updated file insertion to include `synced` and `scanned` fields
   - Removed unused UUID import
3. **`src/services/migration-engine.js`**:
   - Updated database operations to use simplified column names
   - Removed references to detailed permission tracking columns
4. **`src/routes/migration-routes.js`**:
   - Updated queries to use `synced` instead of `synced_permissions`
   - Simplified column selections
5. **`database/lark_google_files_view.sql`**:
   - Updated views to use new simplified columns
   - Removed size column references

### 4. Database Views Updated

#### Views Modified:

- **`lark_google_files_with_permissions`**: Updated to use new column names
- **`matched_lark_google_files`**: Inherits changes from main view
- **`lark_google_sync_stats`**: Removed size calculations

### 5. Migration Script Created

Created: `database/migrations/20250117_simplify_scanned_lark_files.sql`

This migration script:

- Safely drops existing views and tables
- Creates new simplified table structure
- Recreates indexes and views
- Maintains RLS policies
- Preserves data integrity

## Benefits of Changes

1. **Simplified Schema**: Easier to understand and maintain
2. **Better Performance**: Fewer columns mean faster queries and less storage
3. **Clearer Purpose**: Boolean flags clearly indicate sync and scan status
4. **Reduced Complexity**: Eliminates complex permission tracking logic
5. **Future Flexibility**: Easier to extend for new requirements
6. **Type-Safe Field Usage**: Database constraints ensure correct field usage by type

## Field Usage Rules

### `synced` Field (Files Only):

- **Purpose**: Tracks permission synchronization status for files
- **Usage**: Only set for records where `type = 'file'`
- **Values**: `true` (permissions synced), `false` (not synced), `null` (for non-files)
- **Constraint**: Database enforces that only files can have non-null `synced` values

### `scanned` Field (Folders Only):

- **Purpose**: Tracks scan completion status for folders
- **Usage**: Only set for records where `type = 'folder'`
- **Values**: `true` (folder scanned), `false` (not scanned), `null` (for non-folders)
- **Constraint**: Database enforces that only folders can have non-null `scanned` values

## Usage Notes

### For Developers:

- Use `synced` boolean to check if permissions are synchronized (only applicable for files, type = 'file')
- Use `scanned` boolean to check if folder has been scanned (only applicable for folders, type = 'folder')
- ID field is now auto-incrementing SERIAL instead of UUID

### For Database Operations:

- Foreign key references should use INTEGER type
- No need to generate UUIDs when inserting new records
- Permission details are now tracked in separate `sync_lark_permissions` table if needed
- `synced` field should only be set for files (type = 'file')
- `scanned` field should only be set for folders (type = 'folder')

## Migration Steps

1. Run the migration script: `20250117_simplify_scanned_lark_files.sql`
2. Verify views are working correctly
3. Test application functionality
4. Update any remaining code references to old column names

## Compatibility

This change maintains API compatibility while simplifying the underlying data structure. Existing functionality should continue to work with the new schema.
