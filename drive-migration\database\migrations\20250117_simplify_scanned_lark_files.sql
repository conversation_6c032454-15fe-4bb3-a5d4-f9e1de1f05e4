-- Migration to simplify scanned_lark_files table
-- Date: 2025-01-17
-- Description: Remove detailed permission tracking columns and simplify to basic sync status

-- Start transaction
BEGIN;

-- 1. Drop dependent views first
DROP VIEW IF EXISTS lark_google_sync_stats CASCADE;
DROP VIEW IF EXISTS matched_lark_google_files CASCADE;
DROP VIEW IF EXISTS lark_google_files_with_permissions CASCADE;

-- 2. Drop the foreign key constraint in sync_lark_permissions table
DROP TABLE IF EXISTS sync_lark_permissions CASCADE;

-- 3. Drop the old scanned_lark_files table
DROP TABLE IF EXISTS scanned_lark_files CASCADE;

-- 4. Create the simplified scanned_lark_files table
CREATE TABLE scanned_lark_files (
  id SERIAL PRIMARY KEY,
  token TEXT NOT NULL UNIQUE, -- Lark file token
  name TEXT NOT NULL, -- File name
  full_path TEXT NOT NULL, -- Đường dẫn đầy đủ
  type TEXT NOT NULL, -- File type (file, folder, etc.)
  parent_token TEXT, -- Parent folder token
  url TEXT, -- Link viewed in browser
  user_email TEXT NOT NULL, -- Email của user sở hữu file
  synced BOOLEAN DEFAULT false, -- Trạng thái sync permissions (chỉ dùng cho files)
  scanned BOOLEAN DEFAULT false, -- Trạng thái scan (chỉ dùng cho folders)
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  -- Constraints to ensure proper usage of synced/scanned fields
  CONSTRAINT check_synced_only_for_files CHECK (
    (type = 'file' AND synced IS NOT NULL) OR 
    (type != 'file' AND synced IS NULL)
  ),
  CONSTRAINT check_scanned_only_for_folders CHECK (
    (type = 'folder' AND scanned IS NOT NULL) OR 
    (type != 'folder' AND scanned IS NULL)
  )
);

-- 5. Create the simplified sync_lark_permissions table
CREATE TABLE sync_lark_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lark_file_id INTEGER NOT NULL REFERENCES scanned_lark_files(id) ON DELETE CASCADE,
    google_permission JSONB NOT NULL,
    is_failed BOOLEAN DEFAULT FALSE,
    error_messages JSONB NULL,
    is_skipped BOOLEAN DEFAULT FALSE,
    skipped_messages JSONB NULL,
    is_success BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. Create indexes for the new table
CREATE INDEX idx_scanned_lark_files_token ON scanned_lark_files(token);
CREATE INDEX idx_scanned_lark_files_name ON scanned_lark_files(name);
CREATE INDEX idx_scanned_lark_files_full_path ON scanned_lark_files(full_path);
CREATE INDEX idx_scanned_lark_files_type ON scanned_lark_files(type);
CREATE INDEX idx_scanned_lark_files_parent_token ON scanned_lark_files(parent_token);
CREATE INDEX idx_scanned_lark_files_user_email ON scanned_lark_files(user_email);
CREATE INDEX idx_scanned_lark_files_synced ON scanned_lark_files(synced);
CREATE INDEX idx_scanned_lark_files_scanned ON scanned_lark_files(scanned);
CREATE INDEX idx_scanned_lark_files_created_at ON scanned_lark_files(created_at);

-- 7. Enable RLS
ALTER TABLE scanned_lark_files ENABLE ROW LEVEL SECURITY;
ALTER TABLE sync_lark_permissions ENABLE ROW LEVEL SECURITY;

-- 8. Create RLS policies
CREATE POLICY "Service role full access scanned_lark_files" ON scanned_lark_files
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role full access sync_lark_permissions" ON sync_lark_permissions
    FOR ALL USING (auth.role() = 'service_role');

-- 9. Create trigger for auto-updating updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_scanned_lark_files_updated_at BEFORE UPDATE ON scanned_lark_files
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. Recreate the views with new schema
-- Main view combining Lark and Google Drive files
CREATE OR REPLACE VIEW lark_google_files_with_permissions AS
SELECT 
    -- Lark file information (using simplified column names)
    lf.id,
    lf.token,
    lf.name,
    lf.full_path,
    lf.type,
    lf.parent_token,
    lf.url,
    lf.user_email,
    lf.synced,
    lf.scanned,
    lf.created_at,
    lf.updated_at,
    
    -- Google Drive permissions only
    gf.permissions as google_permissions,
    
    -- Match status and information
    CASE 
        WHEN gf.id IS NOT NULL THEN 'matched'
        ELSE 'lark_only'
    END as match_status
    
FROM scanned_lark_files lf
LEFT JOIN scanned_files gf ON (
    lf.user_email = gf.user_email 
    AND lf.name = gf.name 
    AND lf.full_path = gf.full_path
    AND lf.type != 'folder'  -- Exclude folders from matching
    AND gf.mime_type != 'application/vnd.google-apps.folder'  -- Exclude Google Drive folders
)
WHERE lf.type != 'folder'  -- Only include files, not folders
ORDER BY lf.user_email, lf.full_path, lf.name;

-- View for matched files only (files that exist in both Lark and Google Drive)
CREATE OR REPLACE VIEW matched_lark_google_files AS
SELECT *
FROM lark_google_files_with_permissions
WHERE match_status = 'matched';

-- View for statistics by user
CREATE OR REPLACE VIEW lark_google_sync_stats AS
SELECT 
    user_email,
    COUNT(*) as total_lark_files,
    COUNT(CASE WHEN match_status = 'matched' THEN 1 END) as matched_files,
    COUNT(CASE WHEN match_status = 'lark_only' THEN 1 END) as lark_only_files,
    ROUND(
        CASE 
            WHEN COUNT(*) > 0 THEN 
                (COUNT(CASE WHEN match_status = 'matched' THEN 1 END)::DECIMAL / COUNT(*)) * 100 
            ELSE 0 
        END, 2
    ) as match_percentage
FROM lark_google_files_with_permissions
GROUP BY user_email
ORDER BY user_email;

-- 11. Create indexes on the views for better performance
CREATE INDEX IF NOT EXISTS idx_lark_google_view_user_email 
ON scanned_lark_files(user_email, name, full_path) 
WHERE type != 'folder';

CREATE INDEX IF NOT EXISTS idx_google_files_match_key
ON scanned_files(user_email, name, full_path)
WHERE mime_type != 'application/vnd.google-apps.folder';

-- 12. Add comments
COMMENT ON TABLE scanned_lark_files IS 'Lưu trữ thông tin files đã scan từ Lark Drive (simplified schema)';
COMMENT ON COLUMN scanned_lark_files.token IS 'Lark file token (unique identifier)';
COMMENT ON COLUMN scanned_lark_files.full_path IS 'Đường dẫn đầy đủ của file trong Lark Drive';
COMMENT ON COLUMN scanned_lark_files.synced IS 'Trạng thái đồng bộ permissions (chỉ dùng cho files)';
COMMENT ON COLUMN scanned_lark_files.scanned IS 'Trạng thái scan folder (chỉ dùng cho folders)';
COMMENT ON COLUMN scanned_lark_files.user_email IS 'Email của user sở hữu file trong Lark Drive';

COMMENT ON VIEW lark_google_files_with_permissions IS 'Kết hợp thông tin từ Lark files và Google Drive files dựa trên email, name, full_path';
COMMENT ON VIEW matched_lark_google_files IS 'Chỉ các file tồn tại ở cả Lark và Google Drive';
COMMENT ON VIEW lark_google_sync_stats IS 'Thống kê tình trạng đồng bộ Lark-Google files theo user';

-- 13. Add to realtime publication if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_publication WHERE pubname = 'supabase_realtime') THEN
        ALTER PUBLICATION supabase_realtime ADD TABLE scanned_lark_files;
    END IF;
END $$;

COMMIT;

-- Print success message
SELECT 'Migration completed successfully: scanned_lark_files table simplified' as status;
