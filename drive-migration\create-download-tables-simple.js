#!/usr/bin/env node

/**
 * Simple script để tạo bảng download bằng cách insert dummy data để trigger table creation
 */

import dotenv from 'dotenv';
import { SupabaseClient } from './src/database/supabase.js';

dotenv.config();

async function createDownloadTablesSimple() {
    console.log('🚀 Creating download tables using simple method...');
    console.log('='.repeat(60));
    
    try {
        const supabase = new SupabaseClient();
        const serviceClient = supabase.getServiceClient();
        
        console.log('⚠️ This method requires manual table creation in Supabase dashboard');
        console.log('');
        console.log('📋 Steps to create tables:');
        console.log('1. Go to Supabase Dashboard > SQL Editor');
        console.log('2. Copy and paste the following SQL:');
        console.log('');
        console.log('-- CREATE DOWNLOAD SESSIONS TABLE');
        console.log(`CREATE TABLE IF NOT EXISTS ${process.env.DOWNLOAD_SESSIONS_TABLE_NAME} (`);
        console.log('  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),');
        console.log('  name TEXT NOT NULL,');
        console.log('  selected_users TEXT[] NOT NULL,');
        console.log('  download_path TEXT NOT NULL,');
        console.log('  concurrent_downloads INTEGER DEFAULT 3,');
        console.log('  max_retries INTEGER DEFAULT 3,');
        console.log('  skip_mime_types TEXT[] DEFAULT \'{}\',');
        console.log('  processing_order TEXT DEFAULT \'created_at\',');
        console.log('  stop_on_error BOOLEAN DEFAULT false,');
        console.log('  continue_on_error BOOLEAN DEFAULT true,');
        console.log('  status TEXT DEFAULT \'pending\',');
        console.log('  total_files INTEGER DEFAULT 0,');
        console.log('  downloaded_files INTEGER DEFAULT 0,');
        console.log('  failed_files INTEGER DEFAULT 0,');
        console.log('  skipped_files INTEGER DEFAULT 0,');
        console.log('  total_size BIGINT DEFAULT 0,');
        console.log('  downloaded_size BIGINT DEFAULT 0,');
        console.log('  current_user_email TEXT,');
        console.log('  current_file_name TEXT,');
        console.log('  error_log JSONB DEFAULT \'[]\',');
        console.log('  started_at TIMESTAMPTZ,');
        console.log('  completed_at TIMESTAMPTZ,');
        console.log('  created_at TIMESTAMPTZ DEFAULT NOW(),');
        console.log('  updated_at TIMESTAMPTZ DEFAULT NOW()');
        console.log(');');
        console.log('');
        console.log('-- CREATE DOWNLOAD ITEMS TABLE');
        console.log(`CREATE TABLE IF NOT EXISTS ${process.env.DOWNLOAD_ITEMS_TABLE_NAME} (`);
        console.log('  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),');
        console.log(`  download_session_id UUID NOT NULL REFERENCES ${process.env.DOWNLOAD_SESSIONS_TABLE_NAME}(id) ON DELETE CASCADE,`);
        console.log(`  scanned_file_id BIGINT NOT NULL REFERENCES ${process.env.SCANNED_FILES_TABLE_NAME}(id),`);
        console.log('  user_email TEXT NOT NULL,');
        console.log('  file_id TEXT NOT NULL,');
        console.log('  file_name TEXT NOT NULL,');
        console.log('  file_path TEXT NOT NULL,');
        console.log('  local_path TEXT,');
        console.log('  file_size BIGINT DEFAULT 0,');
        console.log('  mime_type TEXT,');
        console.log('  is_folder BOOLEAN DEFAULT FALSE,');
        console.log('  status TEXT DEFAULT \'pending\',');
        console.log('  retry_count INTEGER DEFAULT 0,');
        console.log('  error_message TEXT,');
        console.log('  web_view_link TEXT,');
        console.log('  export_links TEXT,');
        console.log('  download_started_at TIMESTAMPTZ,');
        console.log('  download_completed_at TIMESTAMPTZ,');
        console.log('  download_duration INTEGER,');
        console.log('  created_at TIMESTAMPTZ DEFAULT NOW()');
        console.log(');');
        console.log('');
        console.log('-- ADD DOWNLOAD COLUMNS TO SCANNED FILES');
        console.log(`ALTER TABLE ${process.env.SCANNED_FILES_TABLE_NAME}`);
        console.log('ADD COLUMN IF NOT EXISTS download_status TEXT,');
        console.log('ADD COLUMN IF NOT EXISTS local_path TEXT,');
        console.log('ADD COLUMN IF NOT EXISTS downloaded_at TIMESTAMPTZ;');
        console.log('');
        console.log('3. Run the SQL in Supabase dashboard');
        console.log('4. Come back and run: node test-osp-com-vn-tables.js');
        console.log('');
        
        // Test if tables exist
        console.log('🔍 Checking if tables already exist...');
        
        const tables = [
            process.env.DOWNLOAD_SESSIONS_TABLE_NAME,
            process.env.DOWNLOAD_ITEMS_TABLE_NAME
        ];
        
        let allExist = true;
        
        for (const table of tables) {
            try {
                const { data, error } = await serviceClient
                    .from(table)
                    .select('*')
                    .limit(1);
                    
                if (error && error.code === '42P01') {
                    console.log(`❌ Table ${table} does not exist`);
                    allExist = false;
                } else if (error) {
                    console.log(`⚠️ Table ${table}: ${error.message}`);
                    allExist = false;
                } else {
                    console.log(`✅ Table ${table} exists`);
                }
            } catch (err) {
                console.log(`❌ Table ${table}: ${err.message}`);
                allExist = false;
            }
        }
        
        if (allExist) {
            console.log('');
            console.log('🎉 All download tables exist! You can now use the download functionality.');
            console.log('');
            console.log('🧪 Run tests:');
            console.log('node test-download-logic.js');
        } else {
            console.log('');
            console.log('⚠️ Some tables are missing. Please create them using the SQL above.');
        }
        
    } catch (error) {
        console.error('💥 Error:', error.message);
        process.exit(1);
    }
}

// Run the script
createDownloadTablesSimple();
