import express from "express";
import { supabaseClient } from "../database/supabase.js";
import { larkDriveAPI } from "../api/lark-drive-api.js";

const router = express.Router();

/**
 * Get Lark files for a specific user by email
 * GET /api/lark/users/:email/lark-files
 */
router.get("/users/:email/lark-files", async (req, res) => {
  const { email } = req.params;
  const { refresh = false, includeTree = true } = req.query;

  if (!email) {
    return res.status(400).json({ error: "User email is required" });
  }

  console.log(`📁 Getting Lark files for user: ${email}`);

  try {
    // First, get the user's Lark folder information from database
    const { data: user, error: userError } = await supabaseClient
      .getServiceClient()
      .from(process.env.SCANNED_USERS_TABLE_NAME)
      .select("*")
      .eq("primary_email", email)
      .single();

    if (userError) {
      throw new Error(`User lookup error: ${userError.message}`);
    }

    if (!user) {
      return res.status(404).json({
        error: "User not found",
        message: `No user found with email: ${email}`,
      });
    }

    if (!user.lark_user_folder_token) {
      return res.status(404).json({
        error: "No Lark folder found",
        message: `User ${email} does not have an associated Lark folder`,
      });
    }

    console.log(
      `👤 Found user ${email} with Lark folder: ${user.lark_user_folder} (${user.lark_user_folder_token})`
    );

    // Check if we should refresh from Lark API or use cached data
    if (refresh === "true" || refresh === true) {
      try {
        console.log(
          `🔄 Fetching files from Lark Drive folder: ${user.lark_user_folder}`
        );

        // Get all files from the user's Lark folder recursively
        const scanStats = { failedFolders: 0 };
        const larkFiles = await getLarkFolderFilesRecursively(
          user.lark_user_folder_token,
          "",
          scanStats
        );

        console.log(
          `📁 Found ${larkFiles.length} files in Lark folder (${scanStats.failedFolders} folders failed)`
        );

        // Always save to database when refreshing from Lark API
        if (larkFiles.length > 0) {
          console.log(
            `💾 Updating ${larkFiles.length} Lark files in database...`
          );

          const filesToSave = larkFiles.map((file) => ({
            token: file.token,
            name: file.name,
            full_path: file.full_path,
            type: file.type,
            parent_token: file.parent_token,
            url: file.url,
            user_email: email, // Add user email to track ownership
            synced: file.synced, // Only for files
            scanned: file.scanned, // Only for folders, mark as scanned since we're saving from scan
          }));

          // Upsert in batches to avoid timeout and memory issues
          const batchSize = 100; // Process 100 files at a time
          let successCount = 0;
          let errorCount = 0;

          for (let i = 0; i < filesToSave.length; i += batchSize) {
            const batch = filesToSave.slice(i, i + batchSize);
            const batchNumber = Math.floor(i / batchSize) + 1;
            const totalBatches = Math.ceil(filesToSave.length / batchSize);

            try {
              console.log(
                `📦 Processing batch ${batchNumber}/${totalBatches}: ${batch.length} files`
              );

              const { error: batchError } = await supabaseClient
                .getServiceClient()
                .from("scanned_lark_files")
                .upsert(batch, { onConflict: ["token"] });

              if (batchError) {
                console.error(
                  `❌ Error in batch ${batchNumber}:`,
                  batchError.message
                );
                errorCount += batch.length;
              } else {
                console.log(
                  `✅ Successfully processed batch ${batchNumber}: ${batch.length} files`
                );
                successCount += batch.length;
              }
            } catch (batchError) {
              console.error(
                `❌ Exception in batch ${batchNumber}:`,
                batchError.message
              );
              errorCount += batch.length;
            }

            // Small delay between batches to avoid overwhelming the database
            if (i + batchSize < filesToSave.length) {
              await new Promise((resolve) => setTimeout(resolve, 100));
            }
          }

          console.log(
            `📊 Batch upsert completed: ${successCount} success, ${errorCount} errors out of ${filesToSave.length} total files`
          );

          if (errorCount > 0) {
            console.warn(
              `⚠️ Some files failed to save to database: ${errorCount}/${filesToSave.length}`
            );
          }
        }
      } catch (larkError) {
        console.error(`❌ Error fetching from Lark API: ${larkError.message}`);
        throw new Error(
          `Failed to refresh data from Lark API: ${larkError.message}`
        );
      }
    }

    // Try to read from database first
    console.log(`📊 Reading Lark files from database for user ${email}...`);

    // Query files for this specific user using email
    const { data: dbFiles, error: dbError } = await supabaseClient
      .getServiceClient()
      .from("scanned_lark_files")
      .select("*")
      .eq("user_email", email)
      .order("full_path", { ascending: true });

    if (dbError) {
      throw new Error(`Database query error: ${dbError.message}`);
    }

    const larkFiles = dbFiles || [];
    console.log(
      `📊 Found ${larkFiles.length} Lark files in database for user ${email}`
    );

    // Build response
    const response = {
      user: {
        email: user.primary_email,
        fullName: user.full_name,
        larkFolder: user.lark_user_folder,
        larkFolderToken: user.lark_user_folder_token,
      },
      total: larkFiles.length,
      fileCount: larkFiles.filter((file) => file.type === "file").length,
      failedFolderCount: larkFiles.filter(
        (file) => file.type === "folder" && file.scanned === false
      ).length,
      tree: buildLarkFileTree(larkFiles),
      stats: calculateLarkFileStats(larkFiles),
    };

    res.json(response);
  } catch (error) {
    console.error(
      `❌ Error getting Lark files for user ${email}:`,
      error.message
    );
    res.status(500).json({
      error: "Failed to get Lark files for user",
      details: error.message,
    });
  }
});

/**
 * Rescan failed folders for a specific user by email
 * POST /api/lark/users/:email/rescan-failed-folders
 */
router.post("/users/:email/rescan-failed-folders", async (req, res) => {
  const { email } = req.params;

  if (!email) {
    return res.status(400).json({ error: "User email is required" });
  }

  console.log(`🔄 Rescanning failed folders for user: ${email}`);

  try {
    // Get failed folders from database
    const { data: failedFolders, error: failedFoldersError } =
      await supabaseClient
        .getServiceClient()
        .from("scanned_lark_files")
        .select("*")
        .eq("user_email", email)
        .eq("type", "folder")
        .eq("scanned", false)
        .order("full_path", { ascending: true });

    if (failedFoldersError) {
      throw new Error(`Database query error: ${failedFoldersError.message}`);
    }

    if (!failedFolders || failedFolders.length === 0) {
      return res.json({
        success: true,
        message: "No failed folders found to rescan",
        rescannedCount: 0,
        successCount: 0,
        failedCount: 0,
        folders: [],
      });
    }

    console.log(`📁 Found ${failedFolders.length} failed folders to rescan`);

    let successCount = 0;
    let failedCount = 0;
    const rescanResults = [];
    const allFilesToSave = []; // Tạo danh sách file cần thêm bên ngoài vòng lặp
    const foldersToUpdate = []; // Danh sách folder cần update status

    // Giai đoạn 1: Quét tất cả các folder failed
    console.log(
      `📁 Starting scan phase for ${failedFolders.length} failed folders...`
    );

    for (const folder of failedFolders) {
      try {
        console.log(`🔄 Rescanning folder: ${folder.name} (${folder.token})`);

        // Try to scan the folder again
        const scanStats = { failedFolders: 0 };
        const scannedFiles = await getLarkFolderFilesRecursively(
          folder.token,
          folder.full_path.replace(`/${folder.name}`, ""),
          scanStats
        );

        // Nếu quét thành công, thêm folder vào danh sách cần update và files vào danh sách cần save
        if (scannedFiles.length > 0) {
          const filesToSave = scannedFiles.map((file) => ({
            token: file.token,
            name: file.name,
            full_path: file.full_path,
            type: file.type,
            parent_token: file.parent_token,
            url: file.url,
            user_email: email,
            synced: file.synced,
            scanned: file.scanned,
          }));

          allFilesToSave.push(...filesToSave);
        }

        // Thêm folder vào danh sách cần update status
        foldersToUpdate.push({
          token: folder.token,
          status: "success",
        });

        rescanResults.push({
          folder: folder.name,
          token: folder.token,
          status: "success",
          error: null,
          filesFound: scannedFiles.length,
        });

        console.log(
          `✅ Successfully scanned folder: ${folder.name}, found ${scannedFiles.length} files`
        );
      } catch (scanError) {
        console.error(
          `❌ Error rescanning folder ${folder.name}:`,
          scanError.message
        );

        rescanResults.push({
          folder: folder.name,
          token: folder.token,
          status: "failed",
          error: scanError.message,
          filesFound: 0,
        });
      }
    }

    console.log(
      `📊 Scan phase completed. Found ${allFilesToSave.length} total files from ${foldersToUpdate.length} successful folders`
    );

    // Giai đoạn 2: Update folder status trong database
    console.log(`💾 Starting database update phase...`);

    for (const folderUpdate of foldersToUpdate) {
      try {
        const { error: updateError } = await supabaseClient
          .getServiceClient()
          .from("scanned_lark_files")
          .update({
            scanned: true,
          })
          .eq("token", folderUpdate.token);

        if (updateError) {
          console.error(
            `❌ Error updating folder ${folderUpdate.token}:`,
            updateError.message
          );
          failedCount++;

          // Update result to failed
          const resultIndex = rescanResults.findIndex(
            (r) => r.token === folderUpdate.token
          );
          if (resultIndex !== -1) {
            rescanResults[resultIndex].status = "failed";
            rescanResults[
              resultIndex
            ].error = `Database update error: ${updateError.message}`;
          }
        } else {
          successCount++;
        }
      } catch (updateError) {
        console.error(
          `❌ Exception updating folder ${folderUpdate.token}:`,
          updateError.message
        );
        failedCount++;

        // Update result to failed
        const resultIndex = rescanResults.findIndex(
          (r) => r.token === folderUpdate.token
        );
        if (resultIndex !== -1) {
          rescanResults[resultIndex].status = "failed";
          rescanResults[
            resultIndex
          ].error = `Database update exception: ${updateError.message}`;
        }
      }
    }

    // Giai đoạn 3: Insert tất cả files vào database
    if (allFilesToSave.length > 0) {
      console.log(
        `💾 Starting file insertion phase: ${allFilesToSave.length} files...`
      );

      // Upsert files in batches
      const batchSize = 100;
      let insertSuccessCount = 0;
      let insertErrorCount = 0;

      for (let i = 0; i < allFilesToSave.length; i += batchSize) {
        const batch = allFilesToSave.slice(i, i + batchSize);
        const batchNumber = Math.floor(i / batchSize) + 1;
        const totalBatches = Math.ceil(allFilesToSave.length / batchSize);

        try {
          console.log(
            `📦 Processing file batch ${batchNumber}/${totalBatches}: ${batch.length} files`
          );

          const { error: batchError } = await supabaseClient
            .getServiceClient()
            .from("scanned_lark_files")
            .upsert(batch, { onConflict: ["token"] });

          if (batchError) {
            console.error(
              `❌ Error in file batch ${batchNumber}:`,
              batchError.message
            );
            insertErrorCount += batch.length;
          } else {
            console.log(
              `✅ Successfully processed file batch ${batchNumber}: ${batch.length} files`
            );
            insertSuccessCount += batch.length;
          }
        } catch (batchError) {
          console.error(
            `❌ Exception in file batch ${batchNumber}:`,
            batchError.message
          );
          insertErrorCount += batch.length;
        }

        // Small delay between batches
        if (i + batchSize < allFilesToSave.length) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      console.log(
        `📊 File insertion completed: ${insertSuccessCount} success, ${insertErrorCount} errors out of ${allFilesToSave.length} total files`
      );
    } else {
      console.log(`📊 No files to insert into database`);
    }

    console.log(
      `📊 Rescan completed: ${successCount} success, ${failedCount} failed out of ${failedFolders.length} total folders`
    );

    res.json({
      success: true,
      message: `Rescan completed for ${failedFolders.length} folders`,
      rescannedCount: failedFolders.length,
      successCount,
      failedCount,
      folders: rescanResults,
    });
  } catch (error) {
    console.error(
      `❌ Error rescanning failed folders for user ${email}:`,
      error.message
    );
    res.status(500).json({
      error: "Failed to rescan failed folders",
      details: error.message,
    });
  }
});

/**
 * Recursively get all files from a Lark folder
 * @param {string} folderToken - Lark folder token
 * @param {string} basePath - Base path for building full paths
 * @param {Object} scanStats - Object to track scan statistics
 * @returns {Promise<Array>} Array of file objects
 */
async function getLarkFolderFilesRecursively(
  folderToken,
  basePath = "",
  scanStats = { failedFolders: 0 }
) {
  try {
    const allFiles = [];

    // Get immediate children of the folder
    const children = await larkDriveAPI.getFolderChildren(folderToken);

    for (const item of children) {
      const fullPath = basePath ? `${basePath}/${item.name}` : `/${item.name}`;

      const fileObject = {
        token: item.token,
        name: item.name,
        full_path: fullPath,
        type: item.type,
        parent_token: folderToken,
        url: item.url,
        synced: false,
        scanned: false,
      };

      // If it's a folder, recursively get its contents
      if (item.type === "folder") {
        try {
          const subFiles = await getLarkFolderFilesRecursively(
            item.token,
            fullPath,
            scanStats
          );
          allFiles.push(...subFiles);
          fileObject.scanned = true; // Mark folder as scanned successfully
        } catch (subError) {
          console.warn(
            `⚠️ Error scanning subfolder ${item.name}: ${subError.message}`
          );
          fileObject.scanned = false;
          if (scanStats) {
            scanStats.failedFolders++;
          }
        }
      }

      allFiles.push(fileObject);
    }

    return allFiles;
  } catch (error) {
    console.error(
      `❌ Error getting folder children for ${folderToken}:`,
      error.message
    );
    throw error;
  }
}

/**
 * Build tree structure from Lark files
 * @param {Array} files - Array of Lark file objects
 * @returns {Array} Tree structure
 */
function buildLarkFileTree(files) {
  const tree = [];
  const pathMap = new Map();

  // Sort files by path depth, then by type (folders first), then by name
  files.sort((a, b) => {
    const aDepth = (a.full_path.match(/\//g) || []).length;
    const bDepth = (b.full_path.match(/\//g) || []).length;
    const depthDiff = aDepth - bDepth;
    if (depthDiff !== 0) return depthDiff;

    // At same depth, sort folders before files
    const aIsFolder = a.type === "folder";
    const bIsFolder = b.type === "folder";
    if (aIsFolder && !bIsFolder) return -1;
    if (!aIsFolder && bIsFolder) return 1;

    // Same type, sort alphabetically
    return a.full_path.toLowerCase().localeCompare(b.full_path.toLowerCase());
  });

  files.forEach((file) => {
    const pathParts = file.full_path
      .split("/")
      .filter((part) => part.length > 0);
    let currentLevel = tree;
    let currentPath = "";

    // Navigate/create path structure
    for (let i = 0; i < pathParts.length - 1; i++) {
      currentPath += "/" + pathParts[i];

      let folder = currentLevel.find(
        (item) => item.type === "folder" && item.name === pathParts[i]
      );

      if (!folder) {
        folder = {
          id: `lark_folder_${currentPath}`,
          name: pathParts[i],
          type: "folder",
          path: currentPath,
          children: [],
          fileCount: 0,
          folderCount: 0,
          totalSize: 0,
        };
        currentLevel.push(folder);
        pathMap.set(currentPath, folder);
      }

      currentLevel = folder.children;
    }

    // Add the file
    const fileNode = {
      ...file,
      children: file.type === "folder" ? [] : undefined,
    };

    currentLevel.push(fileNode);

    // Update parent folder statistics
    let parentPath = "";
    for (let i = 0; i < pathParts.length - 1; i++) {
      parentPath += "/" + pathParts[i];
      const parentFolder = pathMap.get(parentPath);
      if (parentFolder) {
        if (fileNode.type === "folder") {
          parentFolder.folderCount++;
        } else {
          parentFolder.fileCount++;
          parentFolder.totalSize += file.size || 0;
        }
      }
    }
  });

  // Recursively sort children in each folder: folders first, then files, both alphabetically
  function sortTreeLevel(nodes) {
    nodes.sort((a, b) => {
      // Folders before files
      const aIsFolder = a.type === "folder";
      const bIsFolder = b.type === "folder";
      if (aIsFolder && !bIsFolder) return -1;
      if (!aIsFolder && bIsFolder) return 1;

      // Same type, sort alphabetically by name
      return a.name.toLowerCase().localeCompare(b.name.toLowerCase());
    });

    // Recursively sort children
    nodes.forEach((node) => {
      if (node.children && node.children.length > 0) {
        sortTreeLevel(node.children);
      }
    });
  }

  // Sort the tree structure
  sortTreeLevel(tree);

  return tree;
}

/**
 * Calculate statistics for Lark files
 * @param {Array} files - Array of Lark file objects
 * @returns {Object} Statistics object
 */
function calculateLarkFileStats(files) {
  const stats = {
    totalFiles: 0,
    totalFolders: 0,
    totalSize: 0,
    fileTypes: {},
    byOwner: {},
  };

  files.forEach((file) => {
    if (file.type === "folder") {
      stats.totalFolders++;
    } else {
      stats.totalFiles++;
      stats.totalSize += file.size || 0;

      // Count by file extension
      const extension =
        file.name.split(".").pop()?.toLowerCase() || "no-extension";
      stats.fileTypes[extension] = (stats.fileTypes[extension] || 0) + 1;

      // Count by owner if available
      if (file.owners && file.owners.length > 0) {
        const owner = file.owners[0].name || "unknown";
        stats.byOwner[owner] = (stats.byOwner[owner] || 0) + 1;
      }
    }
  });

  return stats;
}

export default router;
