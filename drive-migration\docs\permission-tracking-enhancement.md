# Permission Tracking Enhancement for scanned_lark_files Table

## Summary

Added four new columns to the `scanned_lark_files` table to better track permission synchronization status:

## New Columns Added

### 1. successful_permissions (INTEGER)

- **Purpose**: Tracks the number of permissions that were successfully synchronized
- **Default Value**: 0
- **Use Case**: Count how many permissions were set successfully during sync

### 2. failed_permissions (INTEGER)

- **Purpose**: Tracks the number of permissions that failed to synchronize
- **Default Value**: 0
- **Use Case**: Count how many permissions encountered errors during sync

### 3. skipped_permissions (INTEGER)

- **Purpose**: Tracks the number of permissions that were skipped during synchronization
- **Default Value**: 0
- **Use Case**: Count how many permissions were intentionally skipped

### 4. skipped_messages (JSONB)

- **Purpose**: Stores detailed information about skipped permissions
- **Default Value**: NULL
- **Use Case**: Store detailed messages/reasons why permissions were skipped
- **Example Structure**:
  ```json
  {
    "skipped_users": [
      {
        "email": "<EMAIL>",
        "reason": "User not found in Lark workspace",
        "original_permission": "viewer"
      }
    ],
    "total_skipped": 1,
    "timestamp": "2025-07-17T10:00:00Z"
  }
  ```

## Database Changes Applied

### Schema Update

The main schema file `database/schema.sql` has been updated to include these columns in the table definition.

### Migration Applied

- Migration name: `add_permission_tracking_columns`
- Applied on: July 17, 2025
- Status: ✅ Successful

### Indexes Created

- `idx_scanned_lark_files_failed_permissions` - Partial index for records with failed_permissions > 0
- `idx_scanned_lark_files_skipped_permissions` - Partial index for records with skipped_permissions > 0

## Benefits

1. **Better Error Tracking**: Can now track exactly how many permissions failed
2. **Skip Monitoring**: Monitor how many permissions are being skipped
3. **Detailed Logging**: Store specific reasons for skipped permissions in JSON format
4. **Performance**: Indexed for efficient querying of error/skip cases
5. **Reporting**: Enable better reporting on permission sync success rates

## Usage Examples

### Query files with permission issues

```sql
SELECT token, name, successful_permissions, failed_permissions, skipped_permissions, skipped_messages
FROM scanned_lark_files
WHERE failed_permissions > 0 OR skipped_permissions > 0;
```

### Get permission sync statistics

```sql
SELECT
  COUNT(*) as total_files,
  SUM(successful_permissions) as total_successful_permissions,
  SUM(failed_permissions) as total_failed_permissions,
  SUM(skipped_permissions) as total_skipped_permissions,
  COUNT(*) FILTER (WHERE successful_permissions > 0) as files_with_successful_permissions,
  COUNT(*) FILTER (WHERE failed_permissions > 0) as files_with_failed_permissions,
  COUNT(*) FILTER (WHERE skipped_permissions > 0) as files_with_skipped_permissions
FROM scanned_lark_files;
```

### Update permission tracking (example)

```sql
UPDATE scanned_lark_files
SET
  successful_permissions = 5,
  failed_permissions = 2,
  skipped_permissions = 1,
  skipped_messages = '{"skipped_users": [{"email": "<EMAIL>", "reason": "External user"}]}'::jsonb
WHERE token = 'your_file_token';
```

## Integration Points

These columns should be updated by:

- Permission synchronization services
- Lark file upload processes
- Permission mapping functions
- Error handling routines

## Next Steps

1. Update permission sync logic to populate these fields
2. Create monitoring dashboards using these metrics
3. Add alerting for high failure rates
4. Implement retry logic based on these counters
