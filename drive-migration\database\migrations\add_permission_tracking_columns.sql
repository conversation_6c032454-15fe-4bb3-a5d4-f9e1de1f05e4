-- Migration: Add permission tracking columns to scanned_lark_files table
-- Date: 2025-07-17
-- Description: Add failed_permissions, skipped_permissions, and skipped_messages columns

-- Add the new columns to scanned_lark_files table
ALTER TABLE scanned_lark_files 
ADD COLUMN IF NOT EXISTS failed_permissions INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS skipped_permissions INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS skipped_messages JSONB;

-- Add comments for the new columns
COMMENT ON COLUMN scanned_lark_files.failed_permissions IS 'Số lượng permission đồng bộ lỗi';
COMMENT ON COLUMN scanned_lark_files.skipped_permissions IS 'Số lượng permission bỏ qua';
COMMENT ON COLUMN scanned_lark_files.skipped_messages IS 'Thông tin chi tiết về permission bỏ qua (JSON)';

-- Add indexes for performance if needed
CREATE INDEX IF NOT EXISTS idx_scanned_lark_files_failed_permissions ON scanned_lark_files(failed_permissions) WHERE failed_permissions > 0;
CREATE INDEX IF NOT EXISTS idx_scanned_lark_files_skipped_permissions ON scanned_lark_files(skipped_permissions) WHERE skipped_permissions > 0;
