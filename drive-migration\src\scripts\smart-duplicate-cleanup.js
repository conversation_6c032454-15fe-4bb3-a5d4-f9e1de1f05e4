#!/usr/bin/env node

import { SupabaseClient } from '../database/supabase.js';
import dotenv from 'dotenv';

dotenv.config();

/**
 * Smart script để xử lý duplicate files với foreign key constraints
 * Sẽ cập nhật references tr<PERSON>ớc khi xóa duplicates
 */
class SmartDuplicateRemover {
    constructor() {
        this.supabase = new SupabaseClient();
        this.serviceClient = this.supabase.getServiceClient();
    }

    /**
     * Tìm tất cả file_id bị trùng lặp
     */
    async findDuplicateFileIds() {
        console.log('🔍 Finding duplicate file_ids...');
        
        // L<PERSON>y tất cả file_id bằng cách phân trang
        let allFiles = [];
        let from = 0;
        const pageSize = 1000;
        
        while (true) {
            const { data: pageData, error: pageError } = await this.serviceClient
                .from('scanned_files')
                .select('id, file_id, created_at, name')
                .range(from, from + pageSize - 1)
                .order('file_id');

            if (pageError) throw pageError;
            
            if (!pageData || pageData.length === 0) break;
            
            allFiles = allFiles.concat(pageData);
            if (pageData.length < pageSize) break;
            from += pageSize;
        }

        console.log(`📋 Loaded ${allFiles.length} total records`);

        // Phân nhóm theo file_id
        const fileIdGroups = {};
        allFiles.forEach(file => {
            if (!fileIdGroups[file.file_id]) {
                fileIdGroups[file.file_id] = [];
            }
            fileIdGroups[file.file_id].push(file);
        });

        // Tìm các nhóm có duplicates
        const duplicateGroups = Object.entries(fileIdGroups)
            .filter(([fileId, records]) => records.length > 1)
            .map(([fileId, records]) => {
                // Sắp xếp theo created_at ASC để giữ bản ghi cũ nhất
                records.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
                return {
                    fileId,
                    keepRecord: records[0], // Giữ bản ghi cũ nhất
                    duplicateRecords: records.slice(1) // Xóa các bản ghi còn lại
                };
            });

        console.log(`🔄 Found ${duplicateGroups.length} file_ids with duplicates`);
        
        const totalDuplicates = duplicateGroups.reduce((sum, group) => sum + group.duplicateRecords.length, 0);
        console.log(`🗑️ Total duplicate records to remove: ${totalDuplicates}`);

        return duplicateGroups;
    }

    /**
     * Cập nhật foreign key references
     */
    async updateForeignKeyReferences(keepRecordId, duplicateRecordIds) {
        console.log(`   🔄 Updating foreign key references...`);
        
        const tables = [
            { table: 'download_items', column: 'scanned_file_id' },
            { table: 'upload_items', column: 'scanned_file_id' }
        ];

        for (const { table, column } of tables) {
            try {
                const { error } = await this.serviceClient
                    .from(table)
                    .update({ [column]: keepRecordId })
                    .in(column, duplicateRecordIds);

                if (error) {
                    console.log(`   ⚠️ Warning updating ${table}: ${error.message}`);
                } else {
                    console.log(`   ✅ Updated references in ${table}`);
                }
            } catch (error) {
                console.log(`   ⚠️ Warning updating ${table}: ${error.message}`);
            }
        }
    }

    /**
     * Xóa các bản ghi duplicate
     */
    async removeDuplicates() {
        console.log('🗑️ Starting smart duplicate removal...');
        
        const duplicateGroups = await this.findDuplicateFileIds();
        
        if (duplicateGroups.length === 0) {
            console.log('✅ No duplicates found!');
            return { success: true, deletedCount: 0 };
        }

        let totalDeleted = 0;
        let processedGroups = 0;

        for (const group of duplicateGroups) {
            processedGroups++;
            const { fileId, keepRecord, duplicateRecords } = group;
            
            console.log(`🔄 Processing ${processedGroups}/${duplicateGroups.length}: ${fileId}`);
            console.log(`   📋 Keeping record: ${keepRecord.id} (${keepRecord.created_at})`);
            console.log(`   🗑️ Removing ${duplicateRecords.length} duplicates`);

            // Cập nhật foreign key references
            const duplicateIds = duplicateRecords.map(r => r.id);
            await this.updateForeignKeyReferences(keepRecord.id, duplicateIds);

            // Xóa các bản ghi duplicate
            try {
                const { error: deleteError } = await this.serviceClient
                    .from('scanned_files')
                    .delete()
                    .in('id', duplicateIds);

                if (deleteError) {
                    console.log(`   ❌ Error deleting duplicates: ${deleteError.message}`);
                    continue;
                }

                totalDeleted += duplicateRecords.length;
                console.log(`   ✅ Deleted ${duplicateRecords.length} duplicates`);
                
            } catch (error) {
                console.log(`   ❌ Error deleting duplicates: ${error.message}`);
                continue;
            }

            // Ngủ một chút để tránh overload
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        console.log(`✅ Completed! Deleted ${totalDeleted} duplicate records`);
        
        return {
            success: true,
            deletedCount: totalDeleted,
            processedGroups: processedGroups
        };
    }

    /**
     * Verify kết quả
     */
    async verifyResults() {
        console.log('🔍 Verifying results...');
        
        const duplicateGroups = await this.findDuplicateFileIds();
        
        if (duplicateGroups.length === 0) {
            console.log('✅ SUCCESS: No duplicate file_ids found!');
            return true;
        } else {
            console.log(`⚠️ WARNING: Still found ${duplicateGroups.length} duplicate file_ids`);
            return false;
        }
    }

    /**
     * Chạy toàn bộ process
     */
    async run() {
        try {
            console.log('🚀 Starting smart duplicate removal process...');
            console.log('');

            // Xóa duplicates
            const result = await this.removeDuplicates();
            console.log('');

            // Verify kết quả
            const verified = await this.verifyResults();
            console.log('');

            if (verified) {
                console.log('🎉 Smart duplicate removal completed successfully!');
            } else {
                console.log('⚠️ Some duplicates may still remain. You may need to run the script again.');
            }
            
        } catch (error) {
            console.error('💥 Script failed:', error.message);
            process.exit(1);
        }
    }
}

// Chạy script nếu được gọi trực tiếp
if (import.meta.url === `file://${process.argv[1]}`) {
    const remover = new SmartDuplicateRemover();
    remover.run();
}

export { SmartDuplicateRemover };
