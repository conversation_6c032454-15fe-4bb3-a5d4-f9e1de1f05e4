-- Migration: Add user_email column to scanned_lark_files table
-- Created: 2025-07-16
-- Purpose: Add user_email column to track which user owns each Lark file

-- Add user_email column to scanned_lark_files table
ALTER TABLE scanned_lark_files 
ADD COLUMN IF NOT EXISTS user_email TEXT NOT NULL DEFAULT '';

-- Update the column to remove default after data migration if needed
-- (This will be handled by the application when populating existing data)

-- Add index for the new user_email column
CREATE INDEX IF NOT EXISTS idx_scanned_lark_files_user_email ON scanned_lark_files(user_email);

-- Add comment for the new column
COMMENT ON COLUMN scanned_lark_files.user_email IS 'Email của user sở hữu file trong Lark Drive';

-- Note: After running this migration, existing records will have empty user_email
-- The application should update these records when refreshing Lark files data
